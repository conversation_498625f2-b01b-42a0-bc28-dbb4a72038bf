#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Product model for محمد الاشرافى POS System
"""

class Product:
    """Product model class"""

    def __init__(self, id=None, name="", category_id=None, price=0, cost=0, 
                 description="", image_path="", barcode="", is_available=True, created_at=None):
        self.id = id
        self.name = name
        self.category_id = category_id
        self.price = price
        self.cost = cost
        self.description = description
        self.image_path = image_path
        self.barcode = barcode
        self.is_available = is_available
        self.created_at = created_at
        self.category_name = ""
        self.quantity = 0

    @staticmethod
    def from_dict(data):
        """Create a product instance from a dictionary"""
        product = Product(
            id=data.get('id'),
            name=data.get('name', ''),
            category_id=data.get('category_id'),
            price=data.get('price', 0),
            cost=data.get('cost', 0),
            description=data.get('description', ''),
            image_path=data.get('image_path', ''),
            barcode=data.get('barcode', ''),
            is_available=bool(data.get('is_available', True)),
            created_at=data.get('created_at')
        )

        # Optional fields that may come from joins
        if 'category_name' in data:
            product.category_name = data.get('category_name', '')

        if 'quantity' in data:
            product.quantity = data.get('quantity', 0)

        return product

    def to_dict(self):
        """Convert product to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'category_id': self.category_id,
            'price': self.price,
            'cost': self.cost,
            'description': self.description,
            'image_path': self.image_path,
            'barcode': self.barcode,
            'is_available': self.is_available,
            'created_at': self.created_at,
            'category_name': self.category_name,
            'quantity': self.quantity
        }
