#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Cafe tab for محمد الاشرافى POS System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QPushButton,
                             QLabel, QScrollArea, QFrame, QSplitter, QTableWidget, 
                             QTableWidgetItem, QHeaderView, QComboBox, QMessageBox,
                             QDialog, QLineEdit, QDoubleSpinBox, QSpinBox)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QFont, QIcon

from src.models.order import Order, OrderItem
from src.models.product import Product
import datetime

class CafeTab(QWidget):
    """Cafe tab class"""

    def __init__(self, db_manager, config):
        """Initialize cafe tab"""
        super().__init__()

        self.db_manager = db_manager
        self.config = config
        self.current_order = Order()
        self.categories = []
        self.products = []

        # Initialize UI
        self.init_ui()

        # Load data
        self.load_categories()

    def init_ui(self):
        """Initialize user interface"""
        # Main layout
        main_layout = QHBoxLayout()
        self.setLayout(main_layout)

        # Left panel - categories and products
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)

        # Categories
        category_label = QLabel("الأصناف")
        category_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        left_layout.addWidget(category_label)

        self.category_buttons_layout = QGridLayout()
        left_layout.addLayout(self.category_buttons_layout)

        # Products
        product_label = QLabel("المنتجات")
        product_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        left_layout.addWidget(product_label)

        products_scroll_area = QScrollArea()
        products_scroll_area.setWidgetResizable(True)
        products_scroll_area.setFrameShape(QFrame.NoFrame)

        self.products_container = QWidget()
        self.products_grid = QGridLayout(self.products_container)
        products_scroll_area.setWidget(self.products_container)

        left_layout.addWidget(products_scroll_area)
        left_layout.setStretchFactor(products_scroll_area, 3)

        # Right panel - current order and actions
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        # Order info
        order_info_layout = QHBoxLayout()
        order_number_label = QLabel("رقم الطلب:")
        self.order_number_value = QLabel("جديد")
        order_info_layout.addWidget(order_number_label)
        order_info_layout.addWidget(self.order_number_value)

        order_date_label = QLabel("التاريخ:")
        self.order_date_value = QLabel(datetime.datetime.now().strftime("%Y-%m-%d %H:%M"))
        order_info_layout.addWidget(order_date_label)
        order_info_layout.addWidget(self.order_date_value)

        right_layout.addLayout(order_info_layout)

        # Order items table
        self.order_table = QTableWidget(0, 5)
        self.order_table.setHorizontalHeaderLabels(["المنتج", "السعر", "الكمية", "الإجمالي", ""])
        self.order_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.order_table.horizontalHeader().setDefaultAlignment(Qt.AlignRight)
        right_layout.addWidget(self.order_table)

        # Order totals
        totals_layout = QGridLayout()

        subtotal_label = QLabel("المجموع الفرعي:")
        subtotal_label.setAlignment(Qt.AlignRight)
        self.subtotal_value = QLabel("0.00")
        totals_layout.addWidget(subtotal_label, 0, 0)
        totals_layout.addWidget(self.subtotal_value, 0, 1)

        tax_label = QLabel("الضريبة:")
        tax_label.setAlignment(Qt.AlignRight)
        self.tax_value = QLabel("0.00")
        totals_layout.addWidget(tax_label, 1, 0)
        totals_layout.addWidget(self.tax_value, 1, 1)

        discount_label = QLabel("الخصم:")
        discount_label.setAlignment(Qt.AlignRight)
        self.discount_value = QLabel("0.00")
        totals_layout.addWidget(discount_label, 2, 0)
        totals_layout.addWidget(self.discount_value, 2, 1)

        total_label = QLabel("الإجمالي:")
        total_label.setAlignment(Qt.AlignRight)
        total_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        self.total_value = QLabel("0.00")
        self.total_value.setStyleSheet("font-weight: bold; font-size: 16px;")
        totals_layout.addWidget(total_label, 3, 0)
        totals_layout.addWidget(self.total_value, 3, 1)

        right_layout.addLayout(totals_layout)

        # Order actions
        actions_layout = QHBoxLayout()

        self.new_order_btn = QPushButton("طلب جديد")
        self.new_order_btn.clicked.connect(self.new_order)
        actions_layout.addWidget(self.new_order_btn)

        self.discount_btn = QPushButton("خصم")
        self.discount_btn.clicked.connect(self.apply_discount)
        actions_layout.addWidget(self.discount_btn)

        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.clicked.connect(self.cancel_order)
        actions_layout.addWidget(self.cancel_btn)

        self.payment_btn = QPushButton("الدفع")
        self.payment_btn.clicked.connect(self.process_payment)
        self.payment_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        actions_layout.addWidget(self.payment_btn)

        right_layout.addLayout(actions_layout)

        # Add panels to main layout
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([int(self.width() * 0.6), int(self.width() * 0.4)])

        main_layout.addWidget(splitter)

    def load_categories(self):
        """Load cafe categories from database"""
        self.categories = self.db_manager.get_all_categories("cafe")
        self.display_categories()

        # Select first category by default
        if self.categories:
            self.select_category(self.categories[0]['id'])

    def display_categories(self):
        """Display category buttons"""
        # Clear existing buttons
        for i in reversed(range(self.category_buttons_layout.count())):
            widget = self.category_buttons_layout.itemAt(i).widget()
            if widget is not None:
                widget.deleteLater()

        # Add category buttons
        row, col = 0, 0
        max_cols = 4

        for category in self.categories:
            button = QPushButton(category['name'])
            button.setMinimumSize(100, 60)
            button.clicked.connect(lambda checked, cat_id=category['id']: self.select_category(cat_id))
            self.category_buttons_layout.addWidget(button, row, col)

            col += 1
            if col >= max_cols:
                col = 0
                row += 1

    def select_category(self, category_id):
        """Select a category and display its products"""
        self.products = self.db_manager.get_all_products(category_id)
        self.display_products()

    def display_products(self):
        """Display products in grid"""
        # Clear existing buttons
        for i in reversed(range(self.products_grid.count())):
            widget = self.products_grid.itemAt(i).widget()
            if widget is not None:
                widget.deleteLater()

        # Add product buttons
        row, col = 0, 0
        max_cols = 3

        for product in self.products:
            button = QPushButton(product['name'] + "\n" + str(product['price']) + " " + self.config.get("currency_symbol", "ريال"))
            button.setMinimumSize(120, 80)
            button.clicked.connect(lambda checked, prod=product: self.add_product_to_order(prod))
            self.products_grid.addWidget(button, row, col)

            col += 1
            if col >= max_cols:
                col = 0
                row += 1

    def add_product_to_order(self, product_data):
        """Add product to current order"""
        # Check if product already exists in order
        for i, item in enumerate(self.current_order.items):
            if item.product_id == product_data['id']:
                # Increment quantity
                item.quantity += 1
                item.calculate_total_price()
                self.update_order_table_item(i)
                self.update_totals()
                return

        # Create new order item
        product = Product.from_dict(product_data)
        order_item = OrderItem(
            product_id=product.id,
            quantity=1,
            unit_price=product.price,
            product_name=product.name
        )
        order_item.calculate_total_price()

        # Add to order
        self.current_order.items.append(order_item)

        # Add to table
        self.add_item_to_table(order_item)

        # Update totals
        self.update_totals()

    def add_item_to_table(self, order_item):
        """Add item to order table"""
        row = self.order_table.rowCount()
        self.order_table.insertRow(row)

        # Product name
        name_item = QTableWidgetItem(order_item.product_name)
        name_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.order_table.setItem(row, 0, name_item)

        # Unit price
        price_item = QTableWidgetItem(f"{order_item.unit_price:.2f}")
        price_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.order_table.setItem(row, 1, price_item)

        # Quantity
        quantity_item = QTableWidgetItem(str(order_item.quantity))
        quantity_item.setTextAlignment(Qt.AlignCenter)
        self.order_table.setItem(row, 2, quantity_item)

        # Total price
        total_item = QTableWidgetItem(f"{order_item.total_price:.2f}")
        total_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.order_table.setItem(row, 3, total_item)

        # Remove button
        remove_button = QPushButton("×")
        remove_button.setMaximumWidth(30)
        remove_button.clicked.connect(lambda: self.remove_item_from_order(row))
        self.order_table.setCellWidget(row, 4, remove_button)

    def update_order_table_item(self, row):
        """Update an existing item in the order table"""
        item = self.current_order.items[row]

        # Update quantity
        quantity_item = QTableWidgetItem(str(item.quantity))
        quantity_item.setTextAlignment(Qt.AlignCenter)
        self.order_table.setItem(row, 2, quantity_item)

        # Update total price
        total_item = QTableWidgetItem(f"{item.total_price:.2f}")
        total_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.order_table.setItem(row, 3, total_item)

    def remove_item_from_order(self, row):
        """Remove item from order at specified row"""
        if 0 <= row < len(self.current_order.items):
            # Remove from order items
            self.current_order.items.pop(row)

            # Remove from table
            self.order_table.removeRow(row)

            # Update totals
            self.update_totals()

    def update_totals(self):
        """Update order totals"""
        # Calculate subtotal
        subtotal = sum(item.total_price for item in self.current_order.items)
        self.current_order.total_amount = subtotal

        # Get tax rate from config
        tax_rate = float(self.config.get("tax_rate", 0))
        tax_amount = subtotal * tax_rate / 100
        self.current_order.tax_amount = tax_amount

        # Calculate final amount
        final_amount = subtotal + tax_amount - self.current_order.discount_amount
        self.current_order.final_amount = final_amount

        # Update display
        currency = self.config.get("currency_symbol", "ريال")
        self.subtotal_value.setText(f"{subtotal:.2f} {currency}")
        self.tax_value.setText(f"{tax_amount:.2f} {currency}")
        self.discount_value.setText(f"{self.current_order.discount_amount:.2f} {currency}")
        self.total_value.setText(f"{final_amount:.2f} {currency}")

    def new_order(self):
        """Start a new order"""
        self.current_order = Order()
        self.order_number_value.setText("جديد")
        self.order_date_value.setText(datetime.datetime.now().strftime("%Y-%m-%d %H:%M"))

        # Clear order table
        self.order_table.setRowCount(0)

        # Reset totals
        self.update_totals()

    def apply_discount(self):
        """Apply discount to order"""
        # Simple dialog to get discount amount
        dialog = QDialog(self)
        dialog.setWindowTitle("تطبيق خصم")
        dialog.setFixedSize(300, 150)

        layout = QVBoxLayout(dialog)

        discount_layout = QHBoxLayout()
        discount_label = QLabel("قيمة الخصم:")
        discount_spin = QDoubleSpinBox()
        discount_spin.setRange(0, self.current_order.total_amount)
        discount_spin.setValue(self.current_order.discount_amount)
        discount_spin.setSingleStep(1)
        discount_layout.addWidget(discount_label)
        discount_layout.addWidget(discount_spin)

        layout.addLayout(discount_layout)

        buttons_layout = QHBoxLayout()
        ok_button = QPushButton("موافق")
        ok_button.clicked.connect(dialog.accept)
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(dialog.reject)
        buttons_layout.addWidget(ok_button)
        buttons_layout.addWidget(cancel_button)

        layout.addLayout(buttons_layout)

        if dialog.exec_() == QDialog.Accepted:
            self.current_order.discount_amount = discount_spin.value()
            self.update_totals()

    def cancel_order(self):
        """Cancel current order"""
        if not self.current_order.items:
            return

        reply = QMessageBox.question(self, "إلغاء الطلب", "هل أنت متأكد من إلغاء الطلب الحالي؟",
                                   QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.new_order()

    def process_payment(self):
        """Process payment for current order"""
        if not self.current_order.items:
            QMessageBox.warning(self, "تنبيه", "لا يوجد منتجات في الطلب الحالي")
            return

        # Simple payment dialog
        dialog = QDialog(self)
        dialog.setWindowTitle("إتمام عملية الدفع")
        dialog.setFixedSize(400, 300)

        layout = QVBoxLayout(dialog)

        # Order summary
        summary_label = QLabel(f"إجمالي الطلب: {self.current_order.final_amount:.2f} {self.config.get('currency_symbol', 'ريال')}")
        summary_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        summary_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(summary_label)

        # Customer name
        customer_layout = QHBoxLayout()
        customer_label = QLabel("اسم العميل (اختياري):")
        customer_edit = QLineEdit()
        customer_layout.addWidget(customer_label)
        customer_layout.addWidget(customer_edit)
        layout.addLayout(customer_layout)

        # Payment method
        payment_layout = QHBoxLayout()
        payment_label = QLabel("طريقة الدفع:")
        payment_combo = QComboBox()
        payment_combo.addItems(["نقدي", "بطاقة ائتمان", "تحويل بنكي"])
        payment_layout.addWidget(payment_label)
        payment_layout.addWidget(payment_combo)
        layout.addLayout(payment_layout)

        # Amount received (for cash)
        amount_layout = QHBoxLayout()
        amount_label = QLabel("المبلغ المستلم:")
        amount_spin = QDoubleSpinBox()
        amount_spin.setRange(self.current_order.final_amount, 9999999)
        amount_spin.setValue(self.current_order.final_amount)
        amount_layout.addWidget(amount_label)
        amount_layout.addWidget(amount_spin)
        layout.addLayout(amount_layout)

        # Change
        change_layout = QHBoxLayout()
        change_label = QLabel("المتبقي للعميل:")
        change_value = QLabel("0.00")
        change_layout.addWidget(change_label)
        change_layout.addWidget(change_value)
        layout.addLayout(change_layout)

        # Update change when amount received changes
        def update_change():
            change = amount_spin.value() - self.current_order.final_amount
            change_value.setText(f"{change:.2f} {self.config.get('currency_symbol', 'ريال')}")

        amount_spin.valueChanged.connect(update_change)
        update_change()

        # Payment method change handler
        def on_payment_method_changed(index):
            is_cash = index == 0  # Cash is first item
            amount_label.setEnabled(is_cash)
            amount_spin.setEnabled(is_cash)
            change_label.setEnabled(is_cash)
            change_value.setEnabled(is_cash)

        payment_combo.currentIndexChanged.connect(on_payment_method_changed)

        # Buttons
        buttons_layout = QHBoxLayout()
        confirm_button = QPushButton("تأكيد وطباعة الفاتورة")
        confirm_button.clicked.connect(dialog.accept)
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(dialog.reject)
        buttons_layout.addWidget(confirm_button)
        buttons_layout.addWidget(cancel_button)
        layout.addLayout(buttons_layout)

        if dialog.exec_() == QDialog.Accepted:
            # Prepare order data
            order_data = {
                'customer_name': customer_edit.text(),
                'order_type': 'cafe',
                'total_amount': self.current_order.total_amount,
                'discount_amount': self.current_order.discount_amount,
                'tax_amount': self.current_order.tax_amount,
                'final_amount': self.current_order.final_amount,
                'payment_method': payment_combo.currentText(),
                'order_status': 'completed'
            }

            # Save order to database
            order_id = self.db_manager.create_order(order_data, [item.to_dict() for item in self.current_order.items])

            if order_id:
                QMessageBox.information(self, "نجاح", "تم حفظ الطلب بنجاح وطباعة الفاتورة")
                self.new_order()
            else:
                QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حفظ الطلب")
