#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نظام نقاط البيع لمحمد الاشرافى - إصدار محسن مع تصميم جميل
"""

import tkinter as tk
from tkinter import ttk, messagebox, font
import sqlite3
from datetime import datetime
import os
import threading
import time

class POSApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🏪 نظام نقاط البيع - محمد الاشرافى")
        self.root.geometry("1400x900")
        self.root.configure(bg='#2c3e50')
        self.root.state('zoomed')  # ملء الشاشة

        # تكوين الخطوط العربية المحسنة
        self.arabic_font = ('Segoe UI', 11)
        self.title_font = ('Segoe UI', 16, 'bold')
        self.button_font = ('Segoe UI', 12, 'bold')
        self.price_font = ('Segoe UI', 14, 'bold')

        # ألوان التصميم الحديث
        self.colors = {
            'primary': '#3498db',
            'success': '#27ae60',
            'danger': '#e74c3c',
            'warning': '#f39c12',
            'info': '#17a2b8',
            'light': '#f8f9fa',
            'dark': '#2c3e50',
            'secondary': '#6c757d'
        }

        # متغيرات الجلسات النشطة للبلايستيشن
        self.active_sessions = {}
        self.ps_timers = {}
        
        self.setup_database()
        self.create_widgets()
        
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        if not os.path.exists('data'):
            os.makedirs('data')
            
        self.conn = sqlite3.connect('data/pos_database.db')
        self.cursor = self.conn.cursor()
        
        # إنشاء الجداول
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                type TEXT DEFAULT 'cafe'
            )
        ''')
        
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                price REAL NOT NULL,
                category_id INTEGER,
                FOREIGN KEY (category_id) REFERENCES categories (id)
            )
        ''')
        
        # إدراج بيانات تجريبية
        self.cursor.execute("SELECT COUNT(*) FROM categories")
        if self.cursor.fetchone()[0] == 0:
            categories = [
                ('مشروبات ساخنة', 'cafe'),
                ('مشروبات باردة', 'cafe'),
                ('وجبات خفيفة', 'cafe'),
                ('حلويات', 'cafe')
            ]
            self.cursor.executemany("INSERT INTO categories (name, type) VALUES (?, ?)", categories)
            
            products = [
                ('قهوة عربية', 15.0, 1),
                ('شاي أحمر', 10.0, 1),
                ('كابتشينو', 20.0, 1),
                ('عصير برتقال', 12.0, 2),
                ('كولا', 8.0, 2),
                ('ساندويش تونة', 25.0, 3),
                ('كيك شوكولاتة', 18.0, 4)
            ]
            self.cursor.executemany("INSERT INTO products (name, price, category_id) VALUES (?, ?, ?)", products)
            
        self.conn.commit()
        
    def create_widgets(self):
        """إنشاء واجهة المستخدم المحسنة"""
        # الشريط العلوي الجميل مع تدرج
        header_frame = tk.Frame(self.root, bg='#34495e', height=100, relief='raised', bd=3)
        header_frame.pack(fill='x', padx=0, pady=0)
        header_frame.pack_propagate(False)

        # عنوان جميل مع الوقت
        title_frame = tk.Frame(header_frame, bg='#34495e')
        title_frame.pack(fill='both', expand=True)

        title_label = tk.Label(title_frame, text="🏪 نظام نقاط البيع - محمد الاشرافى",
                              font=('Segoe UI', 20, 'bold'), bg='#34495e', fg='#ecf0f1')
        title_label.pack(side='left', padx=20, pady=25)

        # عرض الوقت الحالي
        self.time_label = tk.Label(title_frame, text="",
                                  font=('Segoe UI', 14), bg='#34495e', fg='#bdc3c7')
        self.time_label.pack(side='right', padx=20, pady=25)
        self.update_time()

        # الإطار الرئيسي مع تصميم حديث
        main_frame = tk.Frame(self.root, bg='#ecf0f1', relief='sunken', bd=2)
        main_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # تكوين التبويبات مع تصميم محسن
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TNotebook', background='#ecf0f1', borderwidth=0)
        style.configure('TNotebook.Tab', padding=[20, 10], font=self.button_font)

        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)

        # تبويب المقهى مع تصميم جميل
        self.cafe_frame = tk.Frame(notebook, bg='#ffffff', relief='raised', bd=2)
        notebook.add(self.cafe_frame, text='☕ المقهى')
        self.create_cafe_tab()

        # تبويب البلايستيشن مع تصميم جميل
        self.ps_frame = tk.Frame(notebook, bg='#ffffff', relief='raised', bd=2)
        notebook.add(self.ps_frame, text='🎮 البلايستيشن')
        self.create_playstation_tab()

        # تبويب المخزون مع تصميم جميل
        self.inventory_frame = tk.Frame(notebook, bg='#ffffff', relief='raised', bd=2)
        notebook.add(self.inventory_frame, text='📦 المخزون')
        self.create_inventory_tab()

        # تبويب التقارير
        self.reports_frame = tk.Frame(notebook, bg='#ffffff', relief='raised', bd=2)
        notebook.add(self.reports_frame, text='📊 التقارير')
        self.create_reports_tab()

    def update_time(self):
        """تحديث الوقت الحالي"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=f"🕐 {current_time}")
        self.root.after(1000, self.update_time)
        
    def create_cafe_tab(self):
        """إنشاء تبويب المقهى المحسن"""
        # الإطار الأيسر - المنتجات مع تصميم جميل
        left_frame = tk.Frame(self.cafe_frame, bg='#f8f9fa', relief='raised', bd=3)
        left_frame.pack(side='left', fill='both', expand=True, padx=10, pady=10)

        # عنوان المنتجات مع تصميم جميل
        products_header = tk.Frame(left_frame, bg=self.colors['primary'], height=60)
        products_header.pack(fill='x', padx=5, pady=5)
        products_header.pack_propagate(False)

        products_title = tk.Label(products_header, text="🍽️ قائمة المنتجات",
                                 font=('Segoe UI', 18, 'bold'), bg=self.colors['primary'], fg='white')
        products_title.pack(pady=15)

        # إطار الفئات مع تصميم محسن
        categories_frame = tk.Frame(left_frame, bg='#f8f9fa')
        categories_frame.pack(fill='x', padx=10, pady=10)

        # أزرار الفئات الجميلة
        self.cursor.execute("SELECT * FROM categories WHERE type='cafe'")
        categories = self.cursor.fetchall()

        category_colors = [self.colors['success'], self.colors['info'], self.colors['warning'], self.colors['danger']]

        for i, category in enumerate(categories):
            color = category_colors[i % len(category_colors)]
            btn = tk.Button(categories_frame, text=f"📂 {category[1]}", font=self.button_font,
                           bg=color, fg='white', relief='raised', bd=3,
                           activebackground=color, activeforeground='white',
                           cursor='hand2', pady=10,
                           command=lambda c=category[0]: self.load_products(c))
            btn.grid(row=0, column=i, padx=5, pady=5, sticky='ew')
            categories_frame.grid_columnconfigure(i, weight=1)

        # إطار المنتجات مع خلفية جميلة
        products_container = tk.Frame(left_frame, bg='#f8f9fa')
        products_container.pack(fill='both', expand=True, padx=10, pady=5)

        # إضافة scrollbar للمنتجات
        canvas = tk.Canvas(products_container, bg='#ffffff', highlightthickness=0)
        scrollbar = ttk.Scrollbar(products_container, orient="vertical", command=canvas.yview)
        self.products_frame = tk.Frame(canvas, bg='#ffffff')

        self.products_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.products_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # الإطار الأيمن - الطلب مع تصميم جميل
        right_frame = tk.Frame(self.cafe_frame, bg='#e8f4fd', relief='raised', bd=3, width=450)
        right_frame.pack(side='right', fill='y', padx=10, pady=10)
        right_frame.pack_propagate(False)

        # عنوان الطلب مع تصميم جميل
        order_header = tk.Frame(right_frame, bg=self.colors['info'], height=60)
        order_header.pack(fill='x', padx=5, pady=5)
        order_header.pack_propagate(False)

        order_title = tk.Label(order_header, text="🛒 الطلب الحالي",
                              font=('Segoe UI', 16, 'bold'), bg=self.colors['info'], fg='white')
        order_title.pack(pady=15)

        # جدول الطلب مع تصميم محسن
        tree_frame = tk.Frame(right_frame, bg='#e8f4fd')
        tree_frame.pack(fill='both', expand=True, padx=10, pady=5)

        columns = ('المنتج', 'الكمية', 'السعر', 'المجموع')
        self.order_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=12)

        # تحسين عرض الأعمدة
        column_widths = {'المنتج': 120, 'الكمية': 60, 'السعر': 80, 'المجموع': 90}
        for col in columns:
            self.order_tree.heading(col, text=col)
            self.order_tree.column(col, width=column_widths[col], anchor='center')

        # إضافة scrollbar للجدول
        tree_scroll = ttk.Scrollbar(tree_frame, orient="vertical", command=self.order_tree.yview)
        self.order_tree.configure(yscrollcommand=tree_scroll.set)

        self.order_tree.pack(side='left', fill='both', expand=True)
        tree_scroll.pack(side='right', fill='y')

        # إطار المجاميع مع تصميم جميل
        totals_frame = tk.Frame(right_frame, bg='#d4edda', relief='raised', bd=2)
        totals_frame.pack(fill='x', padx=10, pady=10)

        self.total_label = tk.Label(totals_frame, text="💰 المجموع: 0.00 ريال",
                                   font=('Segoe UI', 16, 'bold'), bg='#d4edda', fg='#155724')
        self.total_label.pack(pady=15)

        # أزرار الإجراءات مع تصميم جميل
        actions_frame = tk.Frame(right_frame, bg='#e8f4fd')
        actions_frame.pack(fill='x', padx=10, pady=10)

        pay_btn = tk.Button(actions_frame, text="💳 الدفع والطباعة", font=self.button_font,
                           bg=self.colors['success'], fg='white', relief='raised', bd=4,
                           activebackground='#1e7e34', activeforeground='white',
                           cursor='hand2', pady=12,
                           command=self.process_payment)
        pay_btn.pack(fill='x', pady=3)

        discount_btn = tk.Button(actions_frame, text="🏷️ تطبيق خصم", font=self.button_font,
                                bg=self.colors['warning'], fg='white', relief='raised', bd=4,
                                activebackground='#d39e00', activeforeground='white',
                                cursor='hand2', pady=12,
                                command=self.apply_discount)
        discount_btn.pack(fill='x', pady=3)

        clear_btn = tk.Button(actions_frame, text="🗑️ مسح الطلب", font=self.button_font,
                             bg=self.colors['danger'], fg='white', relief='raised', bd=4,
                             activebackground='#bd2130', activeforeground='white',
                             cursor='hand2', pady=12,
                             command=self.clear_order)
        clear_btn.pack(fill='x', pady=3)
        
        # تحميل المنتجات الافتراضية
        if categories:
            self.load_products(categories[0][0])
            
        # متغيرات الطلب
        self.current_order = []
        self.order_total = 0.0
        
    def load_products(self, category_id):
        """تحميل المنتجات حسب الفئة مع تصميم جميل"""
        # مسح المنتجات الحالية
        for widget in self.products_frame.winfo_children():
            widget.destroy()

        # تحميل المنتجات الجديدة
        self.cursor.execute("SELECT * FROM products WHERE category_id=?", (category_id,))
        products = self.cursor.fetchall()

        # ألوان مختلفة للمنتجات
        product_colors = [
            '#e74c3c',  # أحمر
            '#3498db',  # أزرق
            '#2ecc71',  # أخضر
            '#f39c12',  # برتقالي
            '#9b59b6',  # بنفسجي
            '#1abc9c'   # تركوازي
        ]

        row, col = 0, 0
        for i, product in enumerate(products):
            color = product_colors[i % len(product_colors)]

            # إطار المنتج مع تصميم جميل
            product_frame = tk.Frame(self.products_frame, bg=color, relief='raised', bd=3)
            product_frame.grid(row=row, column=col, padx=8, pady=8, sticky='ew')

            # اسم المنتج
            name_label = tk.Label(product_frame, text=f"🍽️ {product[1]}",
                                 font=('Segoe UI', 12, 'bold'), bg=color, fg='white')
            name_label.pack(pady=(10, 5))

            # سعر المنتج
            price_label = tk.Label(product_frame, text=f"💰 {product[2]:.2f} ريال",
                                  font=('Segoe UI', 14, 'bold'), bg=color, fg='#fff3cd')
            price_label.pack(pady=(0, 10))

            # زر الإضافة
            add_btn = tk.Button(product_frame, text="➕ إضافة",
                               font=('Segoe UI', 10, 'bold'),
                               bg='white', fg=color, relief='raised', bd=2,
                               activebackground='#f8f9fa', activeforeground=color,
                               cursor='hand2', pady=5,
                               command=lambda p=product: self.add_to_order(p))
            add_btn.pack(pady=(0, 10), padx=10, fill='x')

            col += 1
            if col >= 3:
                col = 0
                row += 1

        # تكوين الأعمدة
        for i in range(3):
            self.products_frame.grid_columnconfigure(i, weight=1)
            
    def add_to_order(self, product):
        """إضافة منتج للطلب"""
        # البحث عن المنتج في الطلب الحالي
        for item in self.current_order:
            if item['id'] == product[0]:
                item['quantity'] += 1
                item['total'] = item['quantity'] * item['price']
                break
        else:
            # إضافة منتج جديد
            self.current_order.append({
                'id': product[0],
                'name': product[1],
                'price': product[2],
                'quantity': 1,
                'total': product[2]
            })
        
        self.update_order_display()
        
    def update_order_display(self):
        """تحديث عرض الطلب"""
        # مسح الجدول
        for item in self.order_tree.get_children():
            self.order_tree.delete(item)
            
        # إضافة العناصر
        self.order_total = 0.0
        for item in self.current_order:
            self.order_tree.insert('', 'end', values=(
                item['name'], item['quantity'], 
                f"{item['price']:.2f}", f"{item['total']:.2f}"
            ))
            self.order_total += item['total']
            
        # تحديث المجموع
        self.total_label.config(text=f"المجموع: {self.order_total:.2f} ريال")
        
    def apply_discount(self):
        """تطبيق خصم على الطلب"""
        if not self.current_order:
            messagebox.showwarning("تحذير", "لا توجد عناصر في الطلب")
            return

        # نافذة إدخال الخصم
        discount_window = tk.Toplevel(self.root)
        discount_window.title("تطبيق خصم")
        discount_window.geometry("300x200")
        discount_window.configure(bg='#f8f9fa')
        discount_window.resizable(False, False)

        # توسيط النافذة
        discount_window.transient(self.root)
        discount_window.grab_set()

        tk.Label(discount_window, text="🏷️ نسبة الخصم (%)",
                font=self.button_font, bg='#f8f9fa').pack(pady=20)

        discount_var = tk.StringVar()
        discount_entry = tk.Entry(discount_window, textvariable=discount_var,
                                 font=self.arabic_font, justify='center', width=20)
        discount_entry.pack(pady=10)
        discount_entry.focus()

        def apply():
            try:
                discount_percent = float(discount_var.get())
                if 0 <= discount_percent <= 100:
                    discount_amount = self.order_total * (discount_percent / 100)
                    self.order_total -= discount_amount
                    self.update_order_display()
                    messagebox.showinfo("نجح", f"تم تطبيق خصم {discount_percent}%\nقيمة الخصم: {discount_amount:.2f} ريال")
                    discount_window.destroy()
                else:
                    messagebox.showerror("خطأ", "نسبة الخصم يجب أن تكون بين 0 و 100")
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال رقم صحيح")

        tk.Button(discount_window, text="تطبيق الخصم", font=self.button_font,
                 bg=self.colors['success'], fg='white', command=apply).pack(pady=20)

    def process_payment(self):
        """معالجة الدفع المحسنة"""
        if not self.current_order:
            messagebox.showwarning("تحذير", "لا توجد عناصر في الطلب")
            return

        # نافذة تأكيد الدفع
        payment_window = tk.Toplevel(self.root)
        payment_window.title("تأكيد الدفع")
        payment_window.geometry("400x300")
        payment_window.configure(bg='#f8f9fa')
        payment_window.resizable(False, False)

        payment_window.transient(self.root)
        payment_window.grab_set()

        # عرض تفاصيل الطلب
        tk.Label(payment_window, text="💳 تأكيد الدفع",
                font=('Segoe UI', 16, 'bold'), bg='#f8f9fa').pack(pady=20)

        details_frame = tk.Frame(payment_window, bg='#e9ecef', relief='raised', bd=2)
        details_frame.pack(padx=20, pady=10, fill='x')

        tk.Label(details_frame, text=f"عدد العناصر: {len(self.current_order)}",
                font=self.arabic_font, bg='#e9ecef').pack(pady=5)
        tk.Label(details_frame, text=f"المبلغ الإجمالي: {self.order_total:.2f} ريال",
                font=self.price_font, bg='#e9ecef', fg='#28a745').pack(pady=5)

        def confirm_payment():
            # حفظ الطلب في قاعدة البيانات (يمكن إضافة هذا لاحقاً)
            messagebox.showinfo("نجح", f"✅ تم الدفع بنجاح!\n💰 المبلغ: {self.order_total:.2f} ريال\n🧾 تم طباعة الفاتورة")
            payment_window.destroy()
            self.clear_order()

        tk.Button(payment_window, text="✅ تأكيد الدفع", font=self.button_font,
                 bg=self.colors['success'], fg='white', pady=10,
                 command=confirm_payment).pack(pady=20)

    def clear_order(self):
        """مسح الطلب"""
        if self.current_order:
            result = messagebox.askyesno("تأكيد", "هل أنت متأكد من مسح الطلب؟")
            if result:
                self.current_order = []
                self.order_total = 0.0
                self.update_order_display()
        else:
            self.current_order = []
            self.order_total = 0.0
            self.update_order_display()
        
    def create_playstation_tab(self):
        """إنشاء تبويب البلايستيشن المحسن"""
        # الهيدر الجميل
        header_frame = tk.Frame(self.ps_frame, bg='#6f42c1', height=80)
        header_frame.pack(fill='x', padx=10, pady=10)
        header_frame.pack_propagate(False)

        title = tk.Label(header_frame, text="🎮 إدارة محطات البلايستيشن",
                        font=('Segoe UI', 18, 'bold'), bg='#6f42c1', fg='white')
        title.pack(side='left', padx=20, pady=25)

        # عرض الوقت الحالي
        self.ps_time_label = tk.Label(header_frame, text="",
                                     font=('Segoe UI', 12), bg='#6f42c1', fg='#e9ecef')
        self.ps_time_label.pack(side='right', padx=20, pady=25)
        self.update_ps_time()

        # الإطار الرئيسي
        main_ps_frame = tk.Frame(self.ps_frame, bg='#f8f9fa')
        main_ps_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # إطار المحطات مع تصميم جميل
        stations_container = tk.Frame(main_ps_frame, bg='#f8f9fa')
        stations_container.pack(fill='both', expand=True, padx=10, pady=10)

        # محطات البلايستيشن مع تصميم محسن
        self.stations_data = [
            {'id': 1, 'name': 'محطة PS5 - 1', 'hourly_rate': 25.0, 'active': False, 'start_time': None},
            {'id': 2, 'name': 'محطة PS5 - 2', 'hourly_rate': 25.0, 'active': True, 'start_time': datetime.now()},
            {'id': 3, 'name': 'محطة PS4 - 1', 'hourly_rate': 20.0, 'active': False, 'start_time': None},
            {'id': 4, 'name': 'محطة PS4 - 2', 'hourly_rate': 20.0, 'active': True, 'start_time': datetime.now()},
            {'id': 5, 'name': 'محطة PS4 - 3', 'hourly_rate': 20.0, 'active': False, 'start_time': None},
            {'id': 6, 'name': 'محطة PS4 - 4', 'hourly_rate': 20.0, 'active': False, 'start_time': None}
        ]

        self.station_frames = {}
        row, col = 0, 0

        for station in self.stations_data:
            # تحديد اللون حسب الحالة
            if station['active']:
                bg_color = '#dc3545'  # أحمر للنشط
                status_text = "🔴 نشط"
                button_text = "⏹️ إيقاف"
                button_color = '#6c757d'
            else:
                bg_color = '#28a745'  # أخضر للمتاح
                status_text = "🟢 متاح"
                button_text = "▶️ تشغيل"
                button_color = '#007bff'

            # إطار المحطة
            station_frame = tk.Frame(stations_container, bg=bg_color, relief='raised', bd=4)
            station_frame.grid(row=row, column=col, padx=15, pady=15, sticky='ew', ipadx=10, ipady=10)
            self.station_frames[station['id']] = station_frame

            # اسم المحطة
            name_label = tk.Label(station_frame, text=station['name'],
                                 font=('Segoe UI', 14, 'bold'), bg=bg_color, fg='white')
            name_label.pack(pady=(10, 5))

            # حالة المحطة
            status_label = tk.Label(station_frame, text=status_text,
                                   font=('Segoe UI', 12, 'bold'), bg=bg_color, fg='white')
            status_label.pack(pady=2)

            # السعر بالساعة
            rate_label = tk.Label(station_frame, text=f"💰 {station['hourly_rate']:.0f} ريال/ساعة",
                                 font=('Segoe UI', 11), bg=bg_color, fg='#fff3cd')
            rate_label.pack(pady=2)

            # الوقت والتكلفة (للمحطات النشطة)
            if station['active']:
                duration = datetime.now() - station['start_time']
                hours = duration.total_seconds() / 3600
                cost = hours * station['hourly_rate']

                time_label = tk.Label(station_frame, text=f"⏱️ {int(hours)}:{int((hours % 1) * 60):02d}",
                                     font=('Segoe UI', 11, 'bold'), bg=bg_color, fg='white')
                time_label.pack(pady=2)

                cost_label = tk.Label(station_frame, text=f"💵 {cost:.2f} ريال",
                                     font=('Segoe UI', 11, 'bold'), bg=bg_color, fg='#fff3cd')
                cost_label.pack(pady=2)

            # زر التحكم
            control_btn = tk.Button(station_frame, text=button_text,
                                   font=('Segoe UI', 10, 'bold'),
                                   bg=button_color, fg='white', relief='raised', bd=3,
                                   activebackground='white', activeforeground=button_color,
                                   cursor='hand2', pady=8,
                                   command=lambda s=station: self.toggle_station(s))
            control_btn.pack(pady=(5, 10), padx=15, fill='x')

            col += 1
            if col >= 3:
                col = 0
                row += 1

        # تكوين الأعمدة
        for i in range(3):
            stations_container.grid_columnconfigure(i, weight=1)

    def update_ps_time(self):
        """تحديث وقت البلايستيشن"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.ps_time_label.config(text=f"🕐 {current_time}")
        self.root.after(1000, self.update_ps_time)

    def toggle_station(self, station):
        """تبديل حالة المحطة"""
        if station['active']:
            # إيقاف المحطة
            duration = datetime.now() - station['start_time']
            hours = duration.total_seconds() / 3600
            cost = hours * station['hourly_rate']

            result = messagebox.askyesno("تأكيد الإيقاف",
                                       f"إيقاف {station['name']}؟\n"
                                       f"المدة: {int(hours)}:{int((hours % 1) * 60):02d}\n"
                                       f"التكلفة: {cost:.2f} ريال")
            if result:
                station['active'] = False
                station['start_time'] = None
                messagebox.showinfo("تم الإيقاف", f"تم إيقاف {station['name']}\nالمبلغ المستحق: {cost:.2f} ريال")
        else:
            # تشغيل المحطة
            station['active'] = True
            station['start_time'] = datetime.now()
            messagebox.showinfo("تم التشغيل", f"تم تشغيل {station['name']}")

        # إعادة إنشاء التبويب لتحديث العرض
        for widget in self.ps_frame.winfo_children():
            widget.destroy()
        self.create_playstation_tab()
            
    def create_inventory_tab(self):
        """إنشاء تبويب المخزون المحسن"""
        # الهيدر
        header_frame = tk.Frame(self.inventory_frame, bg='#fd7e14', height=80)
        header_frame.pack(fill='x', padx=10, pady=10)
        header_frame.pack_propagate(False)

        title = tk.Label(header_frame, text="📦 إدارة المخزون والمنتجات",
                        font=('Segoe UI', 18, 'bold'), bg='#fd7e14', fg='white')
        title.pack(pady=25)

        # الإطار الرئيسي
        main_frame = tk.Frame(self.inventory_frame, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg='#f8f9fa')
        buttons_frame.pack(fill='x', padx=10, pady=10)

        add_btn = tk.Button(buttons_frame, text="➕ إضافة منتج جديد", font=self.button_font,
                           bg=self.colors['success'], fg='white', relief='raised', bd=3,
                           cursor='hand2', pady=8)
        add_btn.pack(side='left', padx=5)

        edit_btn = tk.Button(buttons_frame, text="✏️ تعديل منتج", font=self.button_font,
                            bg=self.colors['primary'], fg='white', relief='raised', bd=3,
                            cursor='hand2', pady=8)
        edit_btn.pack(side='left', padx=5)

        delete_btn = tk.Button(buttons_frame, text="🗑️ حذف منتج", font=self.button_font,
                              bg=self.colors['danger'], fg='white', relief='raised', bd=3,
                              cursor='hand2', pady=8)
        delete_btn.pack(side='left', padx=5)

        # جدول المنتجات المحسن
        tree_frame = tk.Frame(main_frame, bg='#f8f9fa')
        tree_frame.pack(fill='both', expand=True, padx=10, pady=10)

        columns = ('ID', 'اسم المنتج', 'السعر', 'الفئة', 'تاريخ الإضافة')
        inventory_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        # تحسين عرض الأعمدة
        column_widths = {'ID': 50, 'اسم المنتج': 200, 'السعر': 100, 'الفئة': 150, 'تاريخ الإضافة': 150}
        for col in columns:
            inventory_tree.heading(col, text=col)
            inventory_tree.column(col, width=column_widths.get(col, 100), anchor='center')

        # إضافة scrollbar
        tree_scroll = ttk.Scrollbar(tree_frame, orient="vertical", command=inventory_tree.yview)
        inventory_tree.configure(yscrollcommand=tree_scroll.set)

        inventory_tree.pack(side='left', fill='both', expand=True)
        tree_scroll.pack(side='right', fill='y')

        # تحميل البيانات
        self.cursor.execute("""
            SELECT p.id, p.name, p.price, c.name, 'اليوم' as date_added
            FROM products p
            JOIN categories c ON p.category_id = c.id
        """)
        products = self.cursor.fetchall()

        for i, product in enumerate(products):
            # تلوين الصفوف بالتناوب
            tag = 'evenrow' if i % 2 == 0 else 'oddrow'
            inventory_tree.insert('', 'end', values=product, tags=(tag,))

        # تكوين ألوان الصفوف
        inventory_tree.tag_configure('evenrow', background='#f8f9fa')
        inventory_tree.tag_configure('oddrow', background='#ffffff')

    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        # الهيدر
        header_frame = tk.Frame(self.reports_frame, bg='#20c997', height=80)
        header_frame.pack(fill='x', padx=10, pady=10)
        header_frame.pack_propagate(False)

        title = tk.Label(header_frame, text="📊 التقارير والإحصائيات",
                        font=('Segoe UI', 18, 'bold'), bg='#20c997', fg='white')
        title.pack(pady=25)

        # الإطار الرئيسي
        main_frame = tk.Frame(self.reports_frame, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # إحصائيات سريعة
        stats_frame = tk.Frame(main_frame, bg='#f8f9fa')
        stats_frame.pack(fill='x', padx=10, pady=10)

        # بطاقات الإحصائيات
        stats_data = [
            {'title': 'مبيعات اليوم', 'value': '1,250 ريال', 'color': '#28a745', 'icon': '💰'},
            {'title': 'عدد الطلبات', 'value': '45 طلب', 'color': '#17a2b8', 'icon': '📋'},
            {'title': 'محطات نشطة', 'value': '3 محطات', 'color': '#dc3545', 'icon': '🎮'},
            {'title': 'إجمالي المنتجات', 'value': '28 منتج', 'color': '#ffc107', 'icon': '📦'}
        ]

        for i, stat in enumerate(stats_data):
            stat_card = tk.Frame(stats_frame, bg=stat['color'], relief='raised', bd=3)
            stat_card.grid(row=0, column=i, padx=10, pady=10, sticky='ew', ipadx=20, ipady=15)

            icon_label = tk.Label(stat_card, text=stat['icon'],
                                 font=('Segoe UI', 24), bg=stat['color'])
            icon_label.pack()

            value_label = tk.Label(stat_card, text=stat['value'],
                                  font=('Segoe UI', 16, 'bold'), bg=stat['color'], fg='white')
            value_label.pack()

            title_label = tk.Label(stat_card, text=stat['title'],
                                  font=('Segoe UI', 12), bg=stat['color'], fg='white')
            title_label.pack()

            stats_frame.grid_columnconfigure(i, weight=1)

        # منطقة الرسوم البيانية (مبسطة)
        charts_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=2)
        charts_frame.pack(fill='both', expand=True, padx=10, pady=10)

        charts_title = tk.Label(charts_frame, text="📈 الرسوم البيانية",
                               font=('Segoe UI', 16, 'bold'), bg='white')
        charts_title.pack(pady=20)

        # رسم بياني مبسط (نص)
        chart_text = tk.Text(charts_frame, height=10, font=('Courier', 12), bg='#f8f9fa')
        chart_text.pack(padx=20, pady=10, fill='both', expand=True)

        # بيانات تجريبية للرسم البياني
        chart_data = """
📊 مبيعات الأسبوع الماضي:

السبت    ████████████████████ 1,200 ريال
الأحد     ███████████████ 900 ريال
الاثنين   ██████████████████████ 1,400 ريال
الثلاثاء  ████████████ 750 ريال
الأربعاء  ███████████████████ 1,100 ريال
الخميس   ████████████████████████ 1,500 ريال
الجمعة   ██████████████████████████ 1,600 ريال

🎮 استخدام محطات البلايستيشن:
محطة 1: ████████████████ 80% من الوقت
محطة 2: ████████████ 60% من الوقت
محطة 3: ██████████████████████ 90% من الوقت
محطة 4: ████████ 40% من الوقت
        """

        chart_text.insert('1.0', chart_data)
        chart_text.config(state='disabled')
            
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()
        
    def __del__(self):
        """إغلاق قاعدة البيانات"""
        if hasattr(self, 'conn'):
            self.conn.close()

if __name__ == "__main__":
    app = POSApp()
    app.run()
