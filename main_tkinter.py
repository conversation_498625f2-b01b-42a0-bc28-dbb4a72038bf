#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نظام نقاط البيع لمحمد الاشرافى - إصدار محسن مع تصميم جميل
"""

import tkinter as tk
from tkinter import ttk, messagebox, font, filedialog
import sqlite3
from datetime import datetime
import os
import threading
import time
import tempfile
import webbrowser


class InvoicePrinter:
    """نظام طباعة الفواتير"""

    def __init__(self, business_name="محمد الاشرافى - مقهى وألعاب"):
        self.business_name = business_name
        self.business_address = "المملكة العربية السعودية"
        self.business_phone = "+966 50 123 4567"

    def generate_invoice_html(self, order_items, total, discount=0, payment_method="نقدي"):
        """إنشاء فاتورة HTML"""
        invoice_number = f"INV-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        current_date = datetime.now().strftime("%Y-%m-%d")
        current_time = datetime.now().strftime("%H:%M:%S")

        # حساب المجاميع
        subtotal = total + discount

        html_content = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>فاتورة - {invoice_number}</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f8f9fa;
                    direction: rtl;
                }}
                .invoice-container {{
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 10px;
                    box-shadow: 0 0 20px rgba(0,0,0,0.1);
                    overflow: hidden;
                }}
                .invoice-header {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px;
                    text-align: center;
                }}
                .business-name {{
                    font-size: 28px;
                    font-weight: bold;
                    margin-bottom: 10px;
                }}
                .business-info {{
                    font-size: 14px;
                    opacity: 0.9;
                }}
                .invoice-details {{
                    display: flex;
                    justify-content: space-between;
                    padding: 20px 30px;
                    background-color: #f8f9fa;
                    border-bottom: 2px solid #e9ecef;
                }}
                .invoice-number {{
                    font-size: 18px;
                    font-weight: bold;
                    color: #495057;
                }}
                .invoice-date {{
                    color: #6c757d;
                }}
                .items-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin: 0;
                }}
                .items-table th {{
                    background-color: #343a40;
                    color: white;
                    padding: 15px;
                    text-align: center;
                    font-weight: bold;
                }}
                .items-table td {{
                    padding: 12px 15px;
                    text-align: center;
                    border-bottom: 1px solid #dee2e6;
                }}
                .items-table tr:nth-child(even) {{
                    background-color: #f8f9fa;
                }}
                .totals-section {{
                    padding: 20px 30px;
                    background-color: #f8f9fa;
                }}
                .total-row {{
                    display: flex;
                    justify-content: space-between;
                    margin: 8px 0;
                    font-size: 16px;
                }}
                .total-row.final {{
                    font-size: 20px;
                    font-weight: bold;
                    color: #28a745;
                    border-top: 2px solid #28a745;
                    padding-top: 10px;
                    margin-top: 15px;
                }}
                .footer {{
                    text-align: center;
                    padding: 20px;
                    background-color: #343a40;
                    color: white;
                    font-size: 14px;
                }}
                .thank-you {{
                    font-size: 18px;
                    color: #28a745;
                    text-align: center;
                    margin: 20px 0;
                    font-weight: bold;
                }}
                @media print {{
                    body {{ margin: 0; padding: 0; background: white; }}
                    .invoice-container {{ box-shadow: none; }}
                }}
            </style>
        </head>
        <body>
            <div class="invoice-container">
                <div class="invoice-header">
                    <div class="business-name">🏪 {self.business_name}</div>
                    <div class="business-info">
                        📍 {self.business_address}<br>
                        📞 {self.business_phone}
                    </div>
                </div>

                <div class="invoice-details">
                    <div>
                        <div class="invoice-number">📄 رقم الفاتورة: {invoice_number}</div>
                        <div class="invoice-date">📅 التاريخ: {current_date}</div>
                        <div class="invoice-date">🕐 الوقت: {current_time}</div>
                    </div>
                    <div>
                        <div class="invoice-date">💳 طريقة الدفع: {payment_method}</div>
                        <div class="invoice-date">👤 الكاشير: المدير</div>
                    </div>
                </div>

                <table class="items-table">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
        """

        # إضافة عناصر الطلب
        for item in order_items:
            html_content += f"""
                        <tr>
                            <td>{item['name']}</td>
                            <td>{item['quantity']}</td>
                            <td>{item['price']:.2f} ريال</td>
                            <td>{item['total']:.2f} ريال</td>
                        </tr>
            """

        html_content += f"""
                    </tbody>
                </table>

                <div class="totals-section">
                    <div class="total-row">
                        <span>المجموع الفرعي:</span>
                        <span>{subtotal:.2f} ريال</span>
                    </div>
        """

        if discount > 0:
            html_content += f"""
                    <div class="total-row">
                        <span>الخصم:</span>
                        <span>-{discount:.2f} ريال</span>
                    </div>
            """

        html_content += f"""
                    <div class="total-row final">
                        <span>المجموع النهائي:</span>
                        <span>{total:.2f} ريال</span>
                    </div>
                </div>

                <div class="thank-you">
                    🙏 شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى
                </div>

                <div class="footer">
                    تم إنشاء هذه الفاتورة بواسطة نظام نقاط البيع المتطور<br>
                    جميع الحقوق محفوظة © {datetime.now().year}
                </div>
            </div>

            <script>
                // طباعة تلقائية عند فتح الصفحة
                window.onload = function() {{
                    setTimeout(function() {{
                        window.print();
                    }}, 500);
                }}
            </script>
        </body>
        </html>
        """

        return html_content

    def print_invoice(self, order_items, total, discount=0, payment_method="نقدي"):
        """طباعة الفاتورة"""
        try:
            # إنشاء ملف HTML مؤقت
            html_content = self.generate_invoice_html(order_items, total, discount, payment_method)

            # حفظ الملف في مجلد مؤقت
            temp_dir = tempfile.gettempdir()
            invoice_filename = f"invoice_{datetime.now().strftime('%Y%m%d%H%M%S')}.html"
            invoice_path = os.path.join(temp_dir, invoice_filename)

            with open(invoice_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            # فتح الفاتورة في المتصفح للطباعة
            webbrowser.open(f'file://{invoice_path}')

            return True, invoice_path

        except Exception as e:
            return False, str(e)

    def save_invoice(self, order_items, total, discount=0, payment_method="نقدي"):
        """حفظ الفاتورة كملف"""
        try:
            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".html",
                filetypes=[("HTML files", "*.html"), ("All files", "*.*")],
                title="حفظ الفاتورة",
                initialname=f"فاتورة_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
            )

            if filename:
                html_content = self.generate_invoice_html(order_items, total, discount, payment_method)

                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(html_content)

                return True, filename
            else:
                return False, "تم إلغاء العملية"

        except Exception as e:
            return False, str(e)


class POSApp:
    def __init__(self, user_info=None):
        self.root = tk.Tk()
        self.user_info = user_info or {'full_name': 'مستخدم', 'role': 'cashier'}
        self.root.title(f"🏪 نظام نقاط البيع - محمد الاشرافى | {self.user_info['full_name']}")
        self.root.geometry("1400x900")
        self.root.configure(bg='#2c3e50')
        self.root.state('zoomed')  # ملء الشاشة

        # التأكد من ظهور النافذة في المقدمة
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(lambda: self.root.attributes('-topmost', False))
        self.root.focus_force()

        # تكوين الخطوط العربية المحسنة
        self.arabic_font = ('Segoe UI', 11)
        self.title_font = ('Segoe UI', 16, 'bold')
        self.button_font = ('Segoe UI', 12, 'bold')
        self.price_font = ('Segoe UI', 14, 'bold')

        # ألوان التصميم الحديث
        self.colors = {
            'primary': '#3498db',
            'success': '#27ae60',
            'danger': '#e74c3c',
            'warning': '#f39c12',
            'info': '#17a2b8',
            'light': '#f8f9fa',
            'dark': '#2c3e50',
            'secondary': '#6c757d'
        }

        # متغيرات الجلسات النشطة للبلايستيشن
        self.active_sessions = {}
        self.ps_timers = {}
        
        self.setup_database()
        self.create_widgets()
        
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        if not os.path.exists('data'):
            os.makedirs('data')
            
        self.conn = sqlite3.connect('data/pos_database.db')
        self.cursor = self.conn.cursor()
        
        # إنشاء الجداول
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                type TEXT DEFAULT 'cafe'
            )
        ''')
        
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                price REAL NOT NULL,
                category_id INTEGER,
                FOREIGN KEY (category_id) REFERENCES categories (id)
            )
        ''')
        
        # إدراج بيانات تجريبية
        self.cursor.execute("SELECT COUNT(*) FROM categories")
        if self.cursor.fetchone()[0] == 0:
            categories = [
                ('مشروبات ساخنة', 'cafe'),
                ('مشروبات باردة', 'cafe'),
                ('وجبات خفيفة', 'cafe'),
                ('حلويات', 'cafe')
            ]
            self.cursor.executemany("INSERT INTO categories (name, type) VALUES (?, ?)", categories)
            
            products = [
                ('قهوة عربية', 15.0, 1),
                ('شاي أحمر', 10.0, 1),
                ('كابتشينو', 20.0, 1),
                ('عصير برتقال', 12.0, 2),
                ('كولا', 8.0, 2),
                ('ساندويش تونة', 25.0, 3),
                ('كيك شوكولاتة', 18.0, 4)
            ]
            self.cursor.executemany("INSERT INTO products (name, price, category_id) VALUES (?, ?, ?)", products)
            
        self.conn.commit()
        
    def create_widgets(self):
        """إنشاء واجهة المستخدم المحسنة"""
        # الشريط العلوي الجميل مع تدرج
        header_frame = tk.Frame(self.root, bg='#34495e', height=100, relief='raised', bd=3)
        header_frame.pack(fill='x', padx=0, pady=0)
        header_frame.pack_propagate(False)

        # عنوان جميل مع الوقت
        title_frame = tk.Frame(header_frame, bg='#34495e')
        title_frame.pack(fill='both', expand=True)

        title_label = tk.Label(title_frame, text="🏪 نظام نقاط البيع - محمد الاشرافى",
                              font=('Segoe UI', 20, 'bold'), bg='#34495e', fg='#ecf0f1')
        title_label.pack(side='left', padx=20, pady=25)

        # عرض الوقت الحالي
        self.time_label = tk.Label(title_frame, text="",
                                  font=('Segoe UI', 14), bg='#34495e', fg='#bdc3c7')
        self.time_label.pack(side='right', padx=20, pady=25)
        self.update_time()

        # شريط المستخدم
        self.create_user_bar()

    def create_user_bar(self):
        """إنشاء شريط معلومات المستخدم"""
        user_frame = tk.Frame(self.root, bg='#2c3e50', height=50)
        user_frame.pack(fill='x', padx=5, pady=(0, 5))
        user_frame.pack_propagate(False)

        # معلومات المستخدم
        user_info_frame = tk.Frame(user_frame, bg='#2c3e50')
        user_info_frame.pack(side='left', padx=20, pady=10)

        # أيقونة المستخدم حسب الدور
        role_icon = "👨‍💼" if self.user_info['role'] == 'admin' else "👨‍💻"
        user_icon = tk.Label(user_info_frame, text=role_icon, font=('Segoe UI', 16),
                            bg='#2c3e50', fg='white')
        user_icon.pack(side='left', padx=(0, 10))

        # اسم المستخدم
        user_name = tk.Label(user_info_frame, text=f"مرحباً، {self.user_info['full_name']}",
                            font=('Segoe UI', 12, 'bold'), bg='#2c3e50', fg='#ecf0f1')
        user_name.pack(side='left')

        # دور المستخدم
        role_text = "مدير النظام" if self.user_info['role'] == 'admin' else "كاشير"
        user_role = tk.Label(user_info_frame, text=f"({role_text})",
                            font=('Segoe UI', 10), bg='#2c3e50', fg='#bdc3c7')
        user_role.pack(side='left', padx=(5, 0))

        # أزرار التحكم
        controls_frame = tk.Frame(user_frame, bg='#2c3e50')
        controls_frame.pack(side='right', padx=20, pady=10)

        # زر تسجيل الخروج
        logout_button = tk.Button(controls_frame, text="🚪 تسجيل الخروج",
                                 font=('Segoe UI', 10, 'bold'), bg='#e74c3c', fg='white',
                                 activebackground='#c0392b', activeforeground='white',
                                 relief='raised', bd=2, cursor='hand2',
                                 command=self.logout)
        logout_button.pack(side='right', padx=(10, 0))

        # زر إعدادات (للمدير فقط)
        if self.user_info['role'] == 'admin':
            settings_button = tk.Button(controls_frame, text="⚙️ الإعدادات",
                                       font=('Segoe UI', 10, 'bold'), bg='#3498db', fg='white',
                                       activebackground='#2980b9', activeforeground='white',
                                       relief='raised', bd=2, cursor='hand2',
                                       command=self.show_settings)
            settings_button.pack(side='right')

    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno("تأكيد تسجيل الخروج",
                                   "هل تريد تسجيل الخروج من النظام؟")
        if result:
            self.root.quit()
            self.root.destroy()

            # إعادة تشغيل نافذة تسجيل الدخول
            try:
                from login_system import LoginWindow
                login_window = LoginWindow()
                login_window.run()
            except ImportError:
                pass

    def show_settings(self):
        """عرض نافذة الإعدادات (للمدير فقط)"""
        if self.user_info['role'] != 'admin':
            messagebox.showerror("غير مسموح", "هذه الميزة متاحة للمدير فقط")
            return

        settings_window = tk.Toplevel(self.root)
        settings_window.title("⚙️ إعدادات النظام")
        settings_window.geometry("500x400")
        settings_window.configure(bg='#f8f9fa')
        settings_window.resizable(False, False)
        settings_window.transient(self.root)
        settings_window.grab_set()

        # عنوان النافذة
        tk.Label(settings_window, text="⚙️ إعدادات النظام",
                font=('Segoe UI', 16, 'bold'), bg='#f8f9fa').pack(pady=20)

        # إعدادات المتجر
        store_frame = tk.LabelFrame(settings_window, text="🏪 إعدادات المتجر",
                                   font=('Segoe UI', 12, 'bold'), bg='#f8f9fa')
        store_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(store_frame, text="اسم المتجر:", font=('Segoe UI', 10), bg='#f8f9fa').pack(anchor='w', padx=10, pady=5)
        store_name_entry = tk.Entry(store_frame, font=('Segoe UI', 10), width=40)
        store_name_entry.insert(0, "محمد الاشرافى - مقهى وألعاب")
        store_name_entry.pack(padx=10, pady=(0, 10))

        # إعدادات النظام
        system_frame = tk.LabelFrame(settings_window, text="💻 إعدادات النظام",
                                    font=('Segoe UI', 12, 'bold'), bg='#f8f9fa')
        system_frame.pack(fill='x', padx=20, pady=10)

        auto_print_var = tk.BooleanVar(value=True)
        tk.Checkbutton(system_frame, text="طباعة تلقائية للفواتير", variable=auto_print_var,
                      font=('Segoe UI', 10), bg='#f8f9fa').pack(anchor='w', padx=10, pady=5)

        sound_var = tk.BooleanVar(value=True)
        tk.Checkbutton(system_frame, text="تفعيل الأصوات", variable=sound_var,
                      font=('Segoe UI', 10), bg='#f8f9fa').pack(anchor='w', padx=10, pady=5)

        # أزرار التحكم
        buttons_frame = tk.Frame(settings_window, bg='#f8f9fa')
        buttons_frame.pack(pady=20)

        tk.Button(buttons_frame, text="💾 حفظ", font=('Segoe UI', 10, 'bold'),
                 bg='#28a745', fg='white', padx=20, pady=5,
                 command=lambda: messagebox.showinfo("نجح", "تم حفظ الإعدادات")).pack(side='left', padx=10)

        tk.Button(buttons_frame, text="❌ إلغاء", font=('Segoe UI', 10, 'bold'),
                 bg='#6c757d', fg='white', padx=20, pady=5,
                 command=settings_window.destroy).pack(side='left', padx=10)

    def create_new_table(self, table_number):
        """إنشاء طاولة جديدة"""
        self.tables[table_number] = {
            'order': [],
            'total': 0.0,
            'discount': 0.0,
            'created_at': datetime.now(),
            'status': 'active'
        }

    def switch_table(self, table_number):
        """التبديل بين الطاولات"""
        # حفظ الطاولة الحالية
        if self.current_table in self.tables:
            self.tables[self.current_table]['order'] = self.current_order.copy()
            self.tables[self.current_table]['total'] = self.order_total
            self.tables[self.current_table]['discount'] = self.applied_discount

        # التبديل للطاولة الجديدة
        self.current_table = table_number
        if table_number not in self.tables:
            self.create_new_table(table_number)

        # تحميل بيانات الطاولة
        table_data = self.tables[table_number]
        self.current_order = table_data['order'].copy()
        self.order_total = table_data['total']
        self.applied_discount = table_data['discount']

        # تحديث العرض
        self.update_order_display()
        self.update_table_display()

    def get_currency_symbol(self):
        """الحصول على رمز العملة الحالية"""
        return self.currencies[self.current_currency]['symbol']

    def convert_price(self, price_sar):
        """تحويل السعر للعملة المختارة"""
        rate = self.currencies[self.current_currency]['rate']
        return price_sar * rate

    def format_price(self, price):
        """تنسيق السعر مع العملة"""
        converted_price = self.convert_price(price)
        symbol = self.get_currency_symbol()
        return f"{converted_price:.2f} {symbol}"

    def change_currency(self):
        """تغيير العملة"""
        currency_window = tk.Toplevel(self.root)
        currency_window.title("💱 تغيير العملة")
        currency_window.geometry("400x300")
        currency_window.configure(bg='#f8f9fa')
        currency_window.resizable(False, False)
        currency_window.transient(self.root)
        currency_window.grab_set()

        tk.Label(currency_window, text="💱 اختيار العملة",
                font=('Segoe UI', 16, 'bold'), bg='#f8f9fa').pack(pady=20)

        # متغير العملة المختارة
        selected_currency = tk.StringVar(value=self.current_currency)

        # قائمة العملات
        currencies_frame = tk.Frame(currency_window, bg='#f8f9fa')
        currencies_frame.pack(pady=20)

        for code, info in self.currencies.items():
            currency_frame = tk.Frame(currencies_frame, bg='#ffffff', relief='raised', bd=2)
            currency_frame.pack(fill='x', padx=20, pady=5)

            tk.Radiobutton(currency_frame, text=f"{info['name']} ({info['symbol']})",
                          variable=selected_currency, value=code,
                          font=('Segoe UI', 12), bg='#ffffff',
                          activebackground='#e3f2fd').pack(anchor='w', padx=10, pady=8)

        def apply_currency():
            old_currency = self.current_currency
            self.current_currency = selected_currency.get()

            # تحديث جميع الأسعار المعروضة
            self.update_order_display()

            messagebox.showinfo("نجح", f"تم تغيير العملة إلى {self.currencies[self.current_currency]['name']}")
            currency_window.destroy()

        # أزرار التحكم
        buttons_frame = tk.Frame(currency_window, bg='#f8f9fa')
        buttons_frame.pack(pady=20)

        tk.Button(buttons_frame, text="✅ تطبيق", font=('Segoe UI', 10, 'bold'),
                 bg='#28a745', fg='white', padx=20, pady=5,
                 command=apply_currency).pack(side='left', padx=10)

        tk.Button(buttons_frame, text="❌ إلغاء", font=('Segoe UI', 10, 'bold'),
                 bg='#6c757d', fg='white', padx=20, pady=5,
                 command=currency_window.destroy).pack(side='left', padx=10)

    def update_table_display(self):
        """تحديث عرض معلومات الطاولة"""
        if hasattr(self, 'table_info_label'):
            table_count = len([t for t in self.tables.values() if t['status'] == 'active'])
            self.table_info_label.config(text=f"🍽️ الطاولة {self.current_table} | العملة: {self.currencies[self.current_currency]['name']} | الطاولات النشطة: {table_count}")

    def show_tables_manager(self):
        """عرض مدير الطاولات"""
        tables_window = tk.Toplevel(self.root)
        tables_window.title("🍽️ إدارة الطاولات")
        tables_window.geometry("600x500")
        tables_window.configure(bg='#f8f9fa')
        tables_window.resizable(False, False)
        tables_window.transient(self.root)
        tables_window.grab_set()

        tk.Label(tables_window, text="🍽️ إدارة الطاولات",
                font=('Segoe UI', 16, 'bold'), bg='#f8f9fa').pack(pady=20)

        # إطار الطاولات
        tables_frame = tk.Frame(tables_window, bg='#f8f9fa')
        tables_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # عرض الطاولات في شبكة
        for i in range(1, self.max_tables + 1):
            row = (i - 1) // 3
            col = (i - 1) % 3

            # تحديد حالة الطاولة
            if i in self.tables and self.tables[i]['status'] == 'active':
                table_total = self.tables[i]['total']
                items_count = len(self.tables[i]['order'])
                bg_color = '#28a745' if i == self.current_table else '#17a2b8'
                status_text = f"نشطة\n{items_count} عنصر\n{self.format_price(table_total)}"
            else:
                bg_color = '#6c757d'
                status_text = "فارغة"

            table_button = tk.Button(tables_frame, text=f"طاولة {i}\n{status_text}",
                                   font=('Segoe UI', 10, 'bold'), bg=bg_color, fg='white',
                                   width=15, height=4, relief='raised', bd=3,
                                   command=lambda t=i: self.select_table_from_manager(t, tables_window))
            table_button.grid(row=row, column=col, padx=5, pady=5)

        # أزرار التحكم
        controls_frame = tk.Frame(tables_window, bg='#f8f9fa')
        controls_frame.pack(pady=20)

        tk.Button(controls_frame, text="➕ طاولة جديدة", font=('Segoe UI', 10, 'bold'),
                 bg='#007bff', fg='white', padx=15, pady=5,
                 command=lambda: self.create_table_from_manager(tables_window)).pack(side='left', padx=5)

        tk.Button(controls_frame, text="🗑️ مسح الطاولة", font=('Segoe UI', 10, 'bold'),
                 bg='#dc3545', fg='white', padx=15, pady=5,
                 command=lambda: self.clear_table_from_manager(tables_window)).pack(side='left', padx=5)

        tk.Button(controls_frame, text="❌ إغلاق", font=('Segoe UI', 10, 'bold'),
                 bg='#6c757d', fg='white', padx=15, pady=5,
                 command=tables_window.destroy).pack(side='left', padx=5)

    def select_table_from_manager(self, table_number, manager_window):
        """اختيار طاولة من مدير الطاولات"""
        self.switch_table(table_number)
        manager_window.destroy()
        messagebox.showinfo("نجح", f"تم التبديل إلى الطاولة {table_number}")

    def create_table_from_manager(self, manager_window):
        """إنشاء طاولة جديدة من المدير"""
        # البحث عن أول طاولة فارغة
        for i in range(1, self.max_tables + 1):
            if i not in self.tables or self.tables[i]['status'] != 'active':
                self.switch_table(i)
                manager_window.destroy()
                messagebox.showinfo("نجح", f"تم إنشاء الطاولة {i}")
                return

        messagebox.showwarning("تحذير", f"تم الوصول للحد الأقصى من الطاولات ({self.max_tables})")

    def clear_table_from_manager(self, manager_window):
        """مسح طاولة من المدير"""
        if self.current_table in self.tables:
            result = messagebox.askyesno("تأكيد", f"هل تريد مسح الطاولة {self.current_table}؟")
            if result:
                self.tables[self.current_table]['status'] = 'closed'
                self.current_order = []
                self.order_total = 0.0
                self.applied_discount = 0.0
                self.update_order_display()
                manager_window.destroy()
                messagebox.showinfo("نجح", f"تم مسح الطاولة {self.current_table}")

        # الإطار الرئيسي مع تصميم حديث
        main_frame = tk.Frame(self.root, bg='#ecf0f1', relief='sunken', bd=2)
        main_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # تكوين التبويبات مع تصميم محسن
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TNotebook', background='#ecf0f1', borderwidth=0)
        style.configure('TNotebook.Tab', padding=[20, 10], font=self.button_font)

        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)

        # تبويب المقهى مع تصميم جميل
        self.cafe_frame = tk.Frame(notebook, bg='#ffffff', relief='raised', bd=2)
        notebook.add(self.cafe_frame, text='☕ المقهى')
        self.create_cafe_tab()

        # تبويب البلايستيشن مع تصميم جميل
        self.ps_frame = tk.Frame(notebook, bg='#ffffff', relief='raised', bd=2)
        notebook.add(self.ps_frame, text='🎮 البلايستيشن')
        self.create_playstation_tab()

        # تبويب المخزون مع تصميم جميل
        self.inventory_frame = tk.Frame(notebook, bg='#ffffff', relief='raised', bd=2)
        notebook.add(self.inventory_frame, text='📦 المخزون')
        self.create_inventory_tab()

        # تبويب التقارير
        self.reports_frame = tk.Frame(notebook, bg='#ffffff', relief='raised', bd=2)
        notebook.add(self.reports_frame, text='📊 التقارير')
        self.create_reports_tab()

    def update_time(self):
        """تحديث الوقت الحالي"""
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if hasattr(self, 'time_label') and self.time_label.winfo_exists():
                self.time_label.config(text=f"🕐 {current_time}")
                self.root.after(1000, self.update_time)
        except tk.TclError:
            # النافذة مغلقة، توقف عن التحديث
            pass
        
    def create_cafe_tab(self):
        """إنشاء تبويب المقهى المحسن"""
        # شريط التحكم العلوي
        control_bar = tk.Frame(self.cafe_frame, bg='#2c3e50', height=60)
        control_bar.pack(fill='x', padx=0, pady=0)
        control_bar.pack_propagate(False)

        # معلومات الطاولة والعملة
        info_frame = tk.Frame(control_bar, bg='#2c3e50')
        info_frame.pack(side='left', padx=20, pady=15)

        self.table_info_label = tk.Label(info_frame, text="",
                                        font=('Segoe UI', 12, 'bold'), bg='#2c3e50', fg='#ecf0f1')
        self.table_info_label.pack()
        self.update_table_display()

        # أزرار التحكم
        controls_frame = tk.Frame(control_bar, bg='#2c3e50')
        controls_frame.pack(side='right', padx=20, pady=10)

        # زر إدارة الطاولات
        tables_button = tk.Button(controls_frame, text="🍽️ الطاولات",
                                 font=('Segoe UI', 10, 'bold'), bg='#3498db', fg='white',
                                 relief='raised', bd=2, cursor='hand2', padx=15,
                                 command=self.show_tables_manager)
        tables_button.pack(side='right', padx=(5, 0))

        # زر تغيير العملة
        currency_button = tk.Button(controls_frame, text="💱 العملة",
                                   font=('Segoe UI', 10, 'bold'), bg='#f39c12', fg='white',
                                   relief='raised', bd=2, cursor='hand2', padx=15,
                                   command=self.change_currency)
        currency_button.pack(side='right', padx=(5, 0))

        # الإطار الأيسر - المنتجات مع تصميم جميل
        left_frame = tk.Frame(self.cafe_frame, bg='#f8f9fa', relief='raised', bd=3)
        left_frame.pack(side='left', fill='both', expand=True, padx=10, pady=10)

        # عنوان المنتجات مع تصميم جميل
        products_header = tk.Frame(left_frame, bg=self.colors['primary'], height=60)
        products_header.pack(fill='x', padx=5, pady=5)
        products_header.pack_propagate(False)

        products_title = tk.Label(products_header, text="🍽️ قائمة المنتجات",
                                 font=('Segoe UI', 18, 'bold'), bg=self.colors['primary'], fg='white')
        products_title.pack(pady=15)

        # إطار الفئات مع تصميم محسن
        categories_frame = tk.Frame(left_frame, bg='#f8f9fa')
        categories_frame.pack(fill='x', padx=10, pady=10)

        # أزرار الفئات الجميلة
        self.cursor.execute("SELECT * FROM categories WHERE type='cafe'")
        categories = self.cursor.fetchall()

        category_colors = [self.colors['success'], self.colors['info'], self.colors['warning'], self.colors['danger']]

        for i, category in enumerate(categories):
            color = category_colors[i % len(category_colors)]
            btn = tk.Button(categories_frame, text=f"📂 {category[1]}", font=self.button_font,
                           bg=color, fg='white', relief='raised', bd=3,
                           activebackground=color, activeforeground='white',
                           cursor='hand2', pady=10,
                           command=lambda c=category[0]: self.load_products(c))
            btn.grid(row=0, column=i, padx=5, pady=5, sticky='ew')
            categories_frame.grid_columnconfigure(i, weight=1)

        # إطار المنتجات مع خلفية جميلة
        products_container = tk.Frame(left_frame, bg='#f8f9fa')
        products_container.pack(fill='both', expand=True, padx=10, pady=5)

        # إضافة scrollbar للمنتجات
        canvas = tk.Canvas(products_container, bg='#ffffff', highlightthickness=0)
        scrollbar = ttk.Scrollbar(products_container, orient="vertical", command=canvas.yview)
        self.products_frame = tk.Frame(canvas, bg='#ffffff')

        self.products_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.products_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # الإطار الأيمن - الطلب مع تصميم جميل
        right_frame = tk.Frame(self.cafe_frame, bg='#e8f4fd', relief='raised', bd=3, width=450)
        right_frame.pack(side='right', fill='y', padx=10, pady=10)
        right_frame.pack_propagate(False)

        # عنوان الطلب مع تصميم جميل
        order_header = tk.Frame(right_frame, bg=self.colors['info'], height=60)
        order_header.pack(fill='x', padx=5, pady=5)
        order_header.pack_propagate(False)

        order_title = tk.Label(order_header, text="🛒 الطلب الحالي",
                              font=('Segoe UI', 16, 'bold'), bg=self.colors['info'], fg='white')
        order_title.pack(pady=15)

        # جدول الطلب مع تصميم محسن
        tree_frame = tk.Frame(right_frame, bg='#e8f4fd')
        tree_frame.pack(fill='both', expand=True, padx=10, pady=5)

        columns = ('المنتج', 'الكمية', 'السعر', 'المجموع')
        self.order_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=12)

        # تحسين عرض الأعمدة
        column_widths = {'المنتج': 120, 'الكمية': 60, 'السعر': 80, 'المجموع': 90}
        for col in columns:
            self.order_tree.heading(col, text=col)
            self.order_tree.column(col, width=column_widths[col], anchor='center')

        # إضافة scrollbar للجدول
        tree_scroll = ttk.Scrollbar(tree_frame, orient="vertical", command=self.order_tree.yview)
        self.order_tree.configure(yscrollcommand=tree_scroll.set)

        self.order_tree.pack(side='left', fill='both', expand=True)
        tree_scroll.pack(side='right', fill='y')

        # إطار المجاميع مع تصميم جميل
        totals_frame = tk.Frame(right_frame, bg='#d4edda', relief='raised', bd=2)
        totals_frame.pack(fill='x', padx=10, pady=10)

        self.total_label = tk.Label(totals_frame, text="💰 المجموع: 0.00 ريال",
                                   font=('Segoe UI', 16, 'bold'), bg='#d4edda', fg='#155724')
        self.total_label.pack(pady=15)

        # أزرار الإجراءات مع تصميم جميل
        actions_frame = tk.Frame(right_frame, bg='#e8f4fd')
        actions_frame.pack(fill='x', padx=10, pady=10)

        pay_btn = tk.Button(actions_frame, text="💳 الدفع والطباعة", font=self.button_font,
                           bg=self.colors['success'], fg='white', relief='raised', bd=4,
                           activebackground='#1e7e34', activeforeground='white',
                           cursor='hand2', pady=12,
                           command=self.process_payment)
        pay_btn.pack(fill='x', pady=3)

        discount_btn = tk.Button(actions_frame, text="🏷️ تطبيق خصم", font=self.button_font,
                                bg=self.colors['warning'], fg='white', relief='raised', bd=4,
                                activebackground='#d39e00', activeforeground='white',
                                cursor='hand2', pady=12,
                                command=self.apply_discount)
        discount_btn.pack(fill='x', pady=3)

        clear_btn = tk.Button(actions_frame, text="🗑️ مسح الطلب", font=self.button_font,
                             bg=self.colors['danger'], fg='white', relief='raised', bd=4,
                             activebackground='#bd2130', activeforeground='white',
                             cursor='hand2', pady=12,
                             command=self.clear_order)
        clear_btn.pack(fill='x', pady=3)
        
        # تحميل المنتجات الافتراضية
        if categories:
            self.load_products(categories[0][0])
            
        # متغيرات الطلب
        self.current_order = []
        self.order_total = 0.0
        self.applied_discount = 0.0

        # نظام الطباعة
        self.invoice_printer = InvoicePrinter()

        # نظام العملات
        self.currencies = {
            'SAR': {'name': 'ريال سعودي', 'symbol': 'ريال', 'rate': 1.0},
            'USD': {'name': 'دولار أمريكي', 'symbol': '$', 'rate': 0.27},
            'EUR': {'name': 'يورو', 'symbol': '€', 'rate': 0.24},
            'AED': {'name': 'درهم إماراتي', 'symbol': 'درهم', 'rate': 0.98}
        }
        self.current_currency = 'SAR'

        # نظام الطاولات المتعددة
        self.tables = {}
        self.current_table = 1
        self.max_tables = 10

        # إنشاء طاولة افتراضية
        self.create_new_table(1)
        
    def load_products(self, category_id):
        """تحميل المنتجات حسب الفئة مع تصميم جميل"""
        # مسح المنتجات الحالية
        for widget in self.products_frame.winfo_children():
            widget.destroy()

        # تحميل المنتجات الجديدة
        self.cursor.execute("SELECT * FROM products WHERE category_id=?", (category_id,))
        products = self.cursor.fetchall()

        # ألوان مختلفة للمنتجات
        product_colors = [
            '#e74c3c',  # أحمر
            '#3498db',  # أزرق
            '#2ecc71',  # أخضر
            '#f39c12',  # برتقالي
            '#9b59b6',  # بنفسجي
            '#1abc9c'   # تركوازي
        ]

        row, col = 0, 0
        for i, product in enumerate(products):
            color = product_colors[i % len(product_colors)]

            # إطار المنتج مع تصميم جميل
            product_frame = tk.Frame(self.products_frame, bg=color, relief='raised', bd=3)
            product_frame.grid(row=row, column=col, padx=8, pady=8, sticky='ew')

            # اسم المنتج
            name_label = tk.Label(product_frame, text=f"🍽️ {product[1]}",
                                 font=('Segoe UI', 12, 'bold'), bg=color, fg='white')
            name_label.pack(pady=(10, 5))

            # سعر المنتج
            price_label = tk.Label(product_frame, text=f"💰 {self.format_price(product[2])}",
                                  font=('Segoe UI', 14, 'bold'), bg=color, fg='#fff3cd')
            price_label.pack(pady=(0, 10))

            # زر الإضافة
            add_btn = tk.Button(product_frame, text="➕ إضافة",
                               font=('Segoe UI', 10, 'bold'),
                               bg='white', fg=color, relief='raised', bd=2,
                               activebackground='#f8f9fa', activeforeground=color,
                               cursor='hand2', pady=5,
                               command=lambda p=product: self.add_to_order(p))
            add_btn.pack(pady=(0, 10), padx=10, fill='x')

            col += 1
            if col >= 3:
                col = 0
                row += 1

        # تكوين الأعمدة
        for i in range(3):
            self.products_frame.grid_columnconfigure(i, weight=1)
            
    def add_to_order(self, product):
        """إضافة منتج للطلب"""
        # البحث عن المنتج في الطلب الحالي
        for item in self.current_order:
            if item['id'] == product[0]:
                item['quantity'] += 1
                item['total'] = item['quantity'] * item['price']
                break
        else:
            # إضافة منتج جديد
            self.current_order.append({
                'id': product[0],
                'name': product[1],
                'price': product[2],
                'quantity': 1,
                'total': product[2]
            })
        
        self.update_order_display()
        
    def update_order_display(self):
        """تحديث عرض الطلب"""
        # مسح الجدول
        for item in self.order_tree.get_children():
            self.order_tree.delete(item)
            
        # إضافة العناصر
        self.order_total = 0.0
        for item in self.current_order:
            self.order_tree.insert('', 'end', values=(
                item['name'], item['quantity'],
                self.format_price(item['price']), self.format_price(item['total'])
            ))
            self.order_total += item['total']

        # تحديث المجموع مع الخصم
        final_total = self.order_total - self.applied_discount
        if self.applied_discount > 0:
            self.total_label.config(text=f"المجموع: {self.format_price(self.order_total)}\nالخصم: -{self.format_price(self.applied_discount)}\nالإجمالي: {self.format_price(final_total)}")
        else:
            self.total_label.config(text=f"المجموع: {self.format_price(self.order_total)}")

        # تحديث معلومات الطاولة
        self.update_table_display()
        
    def apply_discount(self):
        """تطبيق خصم على الطلب"""
        if not self.current_order:
            messagebox.showwarning("تحذير", "لا توجد عناصر في الطلب")
            return

        # نافذة إدخال الخصم
        discount_window = tk.Toplevel(self.root)
        discount_window.title("تطبيق خصم")
        discount_window.geometry("300x200")
        discount_window.configure(bg='#f8f9fa')
        discount_window.resizable(False, False)

        # توسيط النافذة
        discount_window.transient(self.root)
        discount_window.grab_set()

        tk.Label(discount_window, text="🏷️ نسبة الخصم (%)",
                font=self.button_font, bg='#f8f9fa').pack(pady=20)

        discount_var = tk.StringVar()
        discount_entry = tk.Entry(discount_window, textvariable=discount_var,
                                 font=self.arabic_font, justify='center', width=20)
        discount_entry.pack(pady=10)
        discount_entry.focus()

        def apply():
            try:
                discount_percent = float(discount_var.get())
                if 0 <= discount_percent <= 100:
                    discount_amount = self.order_total * (discount_percent / 100)
                    self.applied_discount = discount_amount
                    self.order_total -= discount_amount
                    self.update_order_display()
                    messagebox.showinfo("نجح", f"تم تطبيق خصم {discount_percent}%\nقيمة الخصم: {discount_amount:.2f} ريال")
                    discount_window.destroy()
                else:
                    messagebox.showerror("خطأ", "نسبة الخصم يجب أن تكون بين 0 و 100")
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال رقم صحيح")

        tk.Button(discount_window, text="تطبيق الخصم", font=self.button_font,
                 bg=self.colors['success'], fg='white', command=apply).pack(pady=20)

    def process_payment(self):
        """معالجة الدفع المحسنة"""
        if not self.current_order:
            messagebox.showwarning("تحذير", "لا توجد عناصر في الطلب")
            return

        # نافذة تأكيد الدفع
        payment_window = tk.Toplevel(self.root)
        payment_window.title("تأكيد الدفع")
        payment_window.geometry("400x300")
        payment_window.configure(bg='#f8f9fa')
        payment_window.resizable(False, False)

        payment_window.transient(self.root)
        payment_window.grab_set()

        # عرض تفاصيل الطلب
        tk.Label(payment_window, text="💳 تأكيد الدفع",
                font=('Segoe UI', 16, 'bold'), bg='#f8f9fa').pack(pady=20)

        details_frame = tk.Frame(payment_window, bg='#e9ecef', relief='raised', bd=2)
        details_frame.pack(padx=20, pady=10, fill='x')

        tk.Label(details_frame, text=f"عدد العناصر: {len(self.current_order)}",
                font=self.arabic_font, bg='#e9ecef').pack(pady=5)
        tk.Label(details_frame, text=f"المبلغ الإجمالي: {self.order_total:.2f} ريال",
                font=self.price_font, bg='#e9ecef', fg='#28a745').pack(pady=5)

        def confirm_payment():
            # إعداد بيانات الفاتورة
            invoice_items = []
            for item in self.current_order:
                invoice_items.append({
                    'name': item['name'],
                    'quantity': item['quantity'],
                    'price': item['price'],
                    'total': item['quantity'] * item['price']
                })

            # نافذة خيارات الطباعة
            print_window = tk.Toplevel(payment_window)
            print_window.title("خيارات الفاتورة")
            print_window.geometry("400x300")
            print_window.configure(bg='#f8f9fa')
            print_window.resizable(False, False)
            print_window.transient(payment_window)
            print_window.grab_set()

            tk.Label(print_window, text="🧾 خيارات الفاتورة",
                    font=('Segoe UI', 16, 'bold'), bg='#f8f9fa').pack(pady=20)

            # خيارات طريقة الدفع
            payment_frame = tk.Frame(print_window, bg='#f8f9fa')
            payment_frame.pack(pady=10)

            tk.Label(payment_frame, text="💳 طريقة الدفع:",
                    font=self.arabic_font, bg='#f8f9fa').pack()

            payment_var = tk.StringVar(value="نقدي")
            payment_methods = ["نقدي", "بطاقة ائتمان", "بطاقة مدى", "تحويل بنكي"]

            for method in payment_methods:
                tk.Radiobutton(payment_frame, text=method, variable=payment_var, value=method,
                              font=self.arabic_font, bg='#f8f9fa').pack(anchor='w')

            def print_and_finish():
                try:
                    # طباعة الفاتورة
                    success, result = self.invoice_printer.print_invoice(
                        invoice_items, self.order_total, self.applied_discount, payment_var.get()
                    )

                    if success:
                        messagebox.showinfo("نجح", f"✅ تم الدفع بنجاح!\n💰 المبلغ: {self.order_total:.2f} ريال\n🖨️ تم فتح الفاتورة للطباعة")
                    else:
                        messagebox.showerror("خطأ في الطباعة", f"فشل في طباعة الفاتورة:\n{result}")

                except Exception as e:
                    messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

                print_window.destroy()
                payment_window.destroy()
                self.clear_order()

            def save_and_finish():
                try:
                    # حفظ الفاتورة
                    success, result = self.invoice_printer.save_invoice(
                        invoice_items, self.order_total, self.applied_discount, payment_var.get()
                    )

                    if success:
                        messagebox.showinfo("نجح", f"✅ تم الدفع بنجاح!\n💰 المبلغ: {self.order_total:.2f} ريال\n💾 تم حفظ الفاتورة في:\n{result}")
                    else:
                        messagebox.showwarning("تحذير", f"تم الدفع بنجاح ولكن:\n{result}")

                except Exception as e:
                    messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

                print_window.destroy()
                payment_window.destroy()
                self.clear_order()

            def finish_without_print():
                messagebox.showinfo("نجح", f"✅ تم الدفع بنجاح!\n💰 المبلغ: {self.order_total:.2f} ريال")
                print_window.destroy()
                payment_window.destroy()
                self.clear_order()

            # أزرار الخيارات
            buttons_frame = tk.Frame(print_window, bg='#f8f9fa')
            buttons_frame.pack(pady=20)

            tk.Button(buttons_frame, text="🖨️ طباعة الفاتورة", font=self.button_font,
                     bg=self.colors['primary'], fg='white', pady=8,
                     command=print_and_finish).pack(fill='x', pady=2)

            tk.Button(buttons_frame, text="💾 حفظ الفاتورة", font=self.button_font,
                     bg=self.colors['info'], fg='white', pady=8,
                     command=save_and_finish).pack(fill='x', pady=2)

            tk.Button(buttons_frame, text="✅ إنهاء بدون طباعة", font=self.button_font,
                     bg=self.colors['success'], fg='white', pady=8,
                     command=finish_without_print).pack(fill='x', pady=2)

        tk.Button(payment_window, text="✅ تأكيد الدفع", font=self.button_font,
                 bg=self.colors['success'], fg='white', pady=10,
                 command=confirm_payment).pack(pady=20)

    def clear_order(self):
        """مسح الطلب"""
        if self.current_order:
            result = messagebox.askyesno("تأكيد", "هل أنت متأكد من مسح الطلب؟")
            if result:
                self.current_order = []
                self.order_total = 0.0
                self.applied_discount = 0.0
                self.update_order_display()
        else:
            self.current_order = []
            self.order_total = 0.0
            self.applied_discount = 0.0
            self.update_order_display()
        
    def create_playstation_tab(self):
        """إنشاء تبويب البلايستيشن المحسن مع تصميم متطور"""
        # الهيدر الجميل مع تدرج
        header_frame = tk.Frame(self.ps_frame, bg='#667eea', height=100, relief='raised', bd=3)
        header_frame.pack(fill='x', padx=0, pady=0)
        header_frame.pack_propagate(False)

        # إطار العنوان والإحصائيات
        title_frame = tk.Frame(header_frame, bg='#667eea')
        title_frame.pack(fill='both', expand=True)

        # العنوان الرئيسي
        title = tk.Label(title_frame, text="🎮 مركز ألعاب البلايستيشن",
                        font=('Segoe UI', 20, 'bold'), bg='#667eea', fg='white')
        title.pack(side='left', padx=20, pady=15)

        # إحصائيات سريعة
        stats_frame = tk.Frame(title_frame, bg='#667eea')
        stats_frame.pack(side='right', padx=20, pady=15)

        # عدد المحطات النشطة
        active_count = sum(1 for station in self.stations_data if station['active'])
        active_label = tk.Label(stats_frame, text=f"🔴 نشط: {active_count}",
                               font=('Segoe UI', 12, 'bold'), bg='#667eea', fg='#ffeb3b')
        active_label.pack(side='top')

        # عرض الوقت الحالي
        self.ps_time_label = tk.Label(stats_frame, text="",
                                     font=('Segoe UI', 11), bg='#667eea', fg='#e3f2fd')
        self.ps_time_label.pack(side='top')
        self.update_ps_time()

        # الإطار الرئيسي مع خلفية متدرجة
        main_ps_frame = tk.Frame(self.ps_frame, bg='#f5f7fa', relief='sunken', bd=2)
        main_ps_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # شريط التحكم العلوي
        control_frame = tk.Frame(main_ps_frame, bg='#e8f4fd', height=60, relief='raised', bd=2)
        control_frame.pack(fill='x', padx=10, pady=5)
        control_frame.pack_propagate(False)

        # أزرار التحكم العامة
        tk.Button(control_frame, text="🔄 تحديث المحطات", font=self.button_font,
                 bg=self.colors['primary'], fg='white', relief='raised', bd=3,
                 cursor='hand2', pady=8,
                 command=self.refresh_stations).pack(side='left', padx=10, pady=10)

        tk.Button(control_frame, text="⏹️ إيقاف جميع المحطات", font=self.button_font,
                 bg=self.colors['danger'], fg='white', relief='raised', bd=3,
                 cursor='hand2', pady=8,
                 command=self.stop_all_stations).pack(side='left', padx=5, pady=10)

        # عرض الإيرادات اليومية
        daily_revenue = self.calculate_daily_revenue()
        revenue_label = tk.Label(control_frame, text=f"💰 إيرادات اليوم: {daily_revenue:.2f} ريال",
                                font=('Segoe UI', 12, 'bold'), bg='#e8f4fd', fg='#28a745')
        revenue_label.pack(side='right', padx=20, pady=15)

        # إطار المحطات مع تصميم جميل
        stations_container = tk.Frame(main_ps_frame, bg='#f5f7fa')
        stations_container.pack(fill='both', expand=True, padx=10, pady=10)

        # محطات البلايستيشن مع تصميم محسن وبيانات أكثر تفصيلاً
        if not hasattr(self, 'stations_data'):
            self.stations_data = [
                {'id': 1, 'name': 'محطة PS5 - VIP', 'type': 'PS5', 'hourly_rate': 30.0, 'active': False, 'start_time': None, 'customer_name': '', 'total_earned': 0.0},
                {'id': 2, 'name': 'محطة PS5 - Premium', 'type': 'PS5', 'hourly_rate': 25.0, 'active': True, 'start_time': datetime.now(), 'customer_name': 'أحمد محمد', 'total_earned': 75.0},
                {'id': 3, 'name': 'محطة PS4 Pro - 1', 'type': 'PS4 Pro', 'hourly_rate': 22.0, 'active': False, 'start_time': None, 'customer_name': '', 'total_earned': 44.0},
                {'id': 4, 'name': 'محطة PS4 Pro - 2', 'type': 'PS4 Pro', 'hourly_rate': 22.0, 'active': True, 'start_time': datetime.now(), 'customer_name': 'سارة أحمد', 'total_earned': 88.0},
                {'id': 5, 'name': 'محطة PS4 - 1', 'type': 'PS4', 'hourly_rate': 18.0, 'active': False, 'start_time': None, 'customer_name': '', 'total_earned': 36.0},
                {'id': 6, 'name': 'محطة PS4 - 2', 'type': 'PS4', 'hourly_rate': 18.0, 'active': False, 'start_time': None, 'customer_name': '', 'total_earned': 54.0}
            ]

        self.station_frames = {}
        self.station_labels = {}  # لحفظ مراجع التسميات للتحديث
        row, col = 0, 0

        for station in self.stations_data:
            # تحديد الألوان والأيقونات حسب نوع المحطة والحالة
            if station['type'] == 'PS5':
                base_color = '#9c27b0'  # بنفسجي للPS5
                icon = '🎮'
            elif station['type'] == 'PS4 Pro':
                base_color = '#2196f3'  # أزرق للPS4 Pro
                icon = '🕹️'
            else:
                base_color = '#4caf50'  # أخضر للPS4
                icon = '🎯'

            if station['active']:
                bg_color = '#e53935'  # أحمر للنشط
                status_text = "🔴 نشط"
                button_text = "⏹️ إيقاف الجلسة"
                button_color = '#757575'
                border_color = '#d32f2f'
            else:
                bg_color = base_color  # لون المحطة الأساسي
                status_text = "🟢 متاح"
                button_text = "▶️ بدء جلسة"
                button_color = '#1976d2'
                border_color = base_color

            # إطار المحطة مع تصميم محسن
            station_frame = tk.Frame(stations_container, bg=bg_color, relief='raised', bd=5)
            station_frame.grid(row=row, column=col, padx=12, pady=12, sticky='ew', ipadx=8, ipady=8)
            station_frame.configure(highlightbackground=border_color, highlightthickness=2)
            self.station_frames[station['id']] = station_frame

            # هيدر المحطة
            header_frame = tk.Frame(station_frame, bg=bg_color)
            header_frame.pack(fill='x', pady=(8, 5))

            # أيقونة ونوع المحطة
            type_label = tk.Label(header_frame, text=f"{icon} {station['type']}",
                                 font=('Segoe UI', 12, 'bold'), bg=bg_color, fg='white')
            type_label.pack(side='top')

            # اسم المحطة
            name_label = tk.Label(station_frame, text=station['name'],
                                 font=('Segoe UI', 14, 'bold'), bg=bg_color, fg='white')
            name_label.pack(pady=(0, 8))

            # معلومات المحطة
            info_frame = tk.Frame(station_frame, bg=bg_color)
            info_frame.pack(fill='x', pady=5)

            # حالة المحطة
            status_label = tk.Label(info_frame, text=status_text,
                                   font=('Segoe UI', 11, 'bold'), bg=bg_color, fg='#ffeb3b')
            status_label.pack()

            # السعر بالساعة
            rate_label = tk.Label(info_frame, text=f"💰 {station['hourly_rate']:.0f} ريال/ساعة",
                                 font=('Segoe UI', 10), bg=bg_color, fg='#e8f5e8')
            rate_label.pack(pady=2)

            # معلومات الجلسة النشطة
            session_frame = tk.Frame(station_frame, bg=bg_color)
            session_frame.pack(fill='x', pady=5)

            if station['active']:
                # اسم العميل
                if station['customer_name']:
                    customer_label = tk.Label(session_frame, text=f"👤 {station['customer_name']}",
                                            font=('Segoe UI', 9), bg=bg_color, fg='white')
                    customer_label.pack()

                # الوقت والتكلفة
                duration = datetime.now() - station['start_time']
                hours = duration.total_seconds() / 3600
                cost = hours * station['hourly_rate']

                time_label = tk.Label(session_frame, text=f"⏱️ {int(hours)}:{int((hours % 1) * 60):02d}:{int((hours * 3600) % 60):02d}",
                                     font=('Segoe UI', 11, 'bold'), bg=bg_color, fg='white')
                time_label.pack(pady=1)

                cost_label = tk.Label(session_frame, text=f"💵 {cost:.2f} ريال",
                                     font=('Segoe UI', 11, 'bold'), bg=bg_color, fg='#fff3cd')
                cost_label.pack(pady=1)

                # حفظ مراجع للتحديث
                self.station_labels[station['id']] = {
                    'time': time_label,
                    'cost': cost_label
                }
            else:
                # إجمالي الأرباح من هذه المحطة
                total_label = tk.Label(session_frame, text=f"📊 إجمالي الأرباح: {station['total_earned']:.2f} ريال",
                                      font=('Segoe UI', 9), bg=bg_color, fg='#e8f5e8')
                total_label.pack(pady=2)

            # زر التحكم مع تصميم محسن
            control_btn = tk.Button(station_frame, text=button_text,
                                   font=('Segoe UI', 10, 'bold'),
                                   bg=button_color, fg='white', relief='raised', bd=4,
                                   activebackground='white', activeforeground=button_color,
                                   cursor='hand2', pady=10,
                                   command=lambda s=station: self.toggle_station(s))
            control_btn.pack(pady=(8, 12), padx=12, fill='x')

            col += 1
            if col >= 3:
                col = 0
                row += 1

        # تكوين الأعمدة
        for i in range(3):
            stations_container.grid_columnconfigure(i, weight=1)

        # بدء تحديث المؤقتات
        self.update_station_timers()

    def update_ps_time(self):
        """تحديث وقت البلايستيشن"""
        try:
            current_time = datetime.now().strftime("%H:%M:%S")
            if hasattr(self, 'ps_time_label') and self.ps_time_label.winfo_exists():
                self.ps_time_label.config(text=f"🕐 {current_time}")
                self.root.after(1000, self.update_ps_time)
        except tk.TclError:
            # النافذة مغلقة، توقف عن التحديث
            pass

    def update_station_timers(self):
        """تحديث مؤقتات المحطات النشطة"""
        for station in self.stations_data:
            if station['active'] and station['id'] in self.station_labels:
                duration = datetime.now() - station['start_time']
                hours = duration.total_seconds() / 3600
                cost = hours * station['hourly_rate']

                # تحديث عرض الوقت
                time_text = f"⏱️ {int(hours)}:{int((hours % 1) * 60):02d}:{int((hours * 3600) % 60):02d}"
                cost_text = f"💵 {cost:.2f} ريال"

                try:
                    self.station_labels[station['id']]['time'].config(text=time_text)
                    self.station_labels[station['id']]['cost'].config(text=cost_text)
                except:
                    pass  # في حالة عدم وجود التسمية

        # تحديث كل ثانية
        try:
            if hasattr(self, 'root') and self.root.winfo_exists():
                self.root.after(1000, self.update_station_timers)
        except tk.TclError:
            # النافذة مغلقة، توقف عن التحديث
            pass

    def calculate_daily_revenue(self):
        """حساب الإيرادات اليومية"""
        total = sum(station['total_earned'] for station in self.stations_data)
        # إضافة الإيرادات من الجلسات النشطة
        for station in self.stations_data:
            if station['active']:
                duration = datetime.now() - station['start_time']
                hours = duration.total_seconds() / 3600
                total += hours * station['hourly_rate']
        return total

    def refresh_stations(self):
        """تحديث عرض المحطات"""
        for widget in self.ps_frame.winfo_children():
            widget.destroy()
        self.create_playstation_tab()

    def stop_all_stations(self):
        """إيقاف جميع المحطات النشطة"""
        active_stations = [s for s in self.stations_data if s['active']]
        if not active_stations:
            messagebox.showinfo("تنبيه", "لا توجد محطات نشطة حالياً")
            return

        result = messagebox.askyesno("تأكيد", f"هل تريد إيقاف جميع المحطات النشطة؟\nعدد المحطات: {len(active_stations)}")
        if result:
            total_revenue = 0
            for station in active_stations:
                duration = datetime.now() - station['start_time']
                hours = duration.total_seconds() / 3600
                cost = hours * station['hourly_rate']
                total_revenue += cost
                station['total_earned'] += cost
                station['active'] = False
                station['start_time'] = None
                station['customer_name'] = ''

            messagebox.showinfo("تم الإيقاف", f"تم إيقاف جميع المحطات\nإجمالي الإيرادات: {total_revenue:.2f} ريال")
            self.refresh_stations()

    def toggle_station(self, station):
        """تبديل حالة المحطة مع واجهة محسنة"""
        if station['active']:
            # إيقاف المحطة
            duration = datetime.now() - station['start_time']
            hours = duration.total_seconds() / 3600
            cost = hours * station['hourly_rate']

            # نافذة تأكيد الإيقاف
            stop_window = tk.Toplevel(self.root)
            stop_window.title("إيقاف المحطة")
            stop_window.geometry("400x350")
            stop_window.configure(bg='#f8f9fa')
            stop_window.resizable(False, False)
            stop_window.transient(self.root)
            stop_window.grab_set()

            # عنوان النافذة
            tk.Label(stop_window, text="⏹️ إيقاف الجلسة",
                    font=('Segoe UI', 16, 'bold'), bg='#f8f9fa', fg='#dc3545').pack(pady=20)

            # تفاصيل الجلسة
            details_frame = tk.Frame(stop_window, bg='#e9ecef', relief='raised', bd=2)
            details_frame.pack(padx=20, pady=10, fill='x')

            tk.Label(details_frame, text=f"🎮 {station['name']}",
                    font=('Segoe UI', 14, 'bold'), bg='#e9ecef').pack(pady=5)
            tk.Label(details_frame, text=f"👤 العميل: {station['customer_name'] or 'غير محدد'}",
                    font=('Segoe UI', 12), bg='#e9ecef').pack(pady=2)
            tk.Label(details_frame, text=f"⏱️ المدة: {int(hours)}:{int((hours % 1) * 60):02d}:{int((hours * 3600) % 60):02d}",
                    font=('Segoe UI', 12), bg='#e9ecef').pack(pady=2)
            tk.Label(details_frame, text=f"💰 السعر: {station['hourly_rate']:.0f} ريال/ساعة",
                    font=('Segoe UI', 12), bg='#e9ecef').pack(pady=2)
            tk.Label(details_frame, text=f"💵 التكلفة الإجمالية: {cost:.2f} ريال",
                    font=('Segoe UI', 14, 'bold'), bg='#e9ecef', fg='#28a745').pack(pady=5)

            def confirm_stop():
                station['active'] = False
                station['start_time'] = None
                station['total_earned'] += cost
                station['customer_name'] = ''
                messagebox.showinfo("تم الإيقاف", f"✅ تم إيقاف {station['name']}\n💰 المبلغ المستحق: {cost:.2f} ريال")
                stop_window.destroy()
                self.refresh_stations()

            # أزرار التحكم
            buttons_frame = tk.Frame(stop_window, bg='#f8f9fa')
            buttons_frame.pack(pady=20)

            tk.Button(buttons_frame, text="✅ تأكيد الإيقاف", font=self.button_font,
                     bg=self.colors['success'], fg='white', pady=10,
                     command=confirm_stop).pack(side='left', padx=10)

            tk.Button(buttons_frame, text="❌ إلغاء", font=self.button_font,
                     bg=self.colors['secondary'], fg='white', pady=10,
                     command=stop_window.destroy).pack(side='left', padx=10)

        else:
            # تشغيل المحطة
            start_window = tk.Toplevel(self.root)
            start_window.title("بدء جلسة جديدة")
            start_window.geometry("350x300")
            start_window.configure(bg='#f8f9fa')
            start_window.resizable(False, False)
            start_window.transient(self.root)
            start_window.grab_set()

            # عنوان النافذة
            tk.Label(start_window, text="▶️ بدء جلسة جديدة",
                    font=('Segoe UI', 16, 'bold'), bg='#f8f9fa', fg='#28a745').pack(pady=20)

            # معلومات المحطة
            tk.Label(start_window, text=f"🎮 {station['name']}",
                    font=('Segoe UI', 14, 'bold'), bg='#f8f9fa').pack(pady=5)
            tk.Label(start_window, text=f"💰 {station['hourly_rate']:.0f} ريال/ساعة",
                    font=('Segoe UI', 12), bg='#f8f9fa', fg='#6c757d').pack(pady=5)

            # إدخال اسم العميل
            tk.Label(start_window, text="👤 اسم العميل (اختياري):",
                    font=self.arabic_font, bg='#f8f9fa').pack(pady=(20, 5))

            customer_var = tk.StringVar()
            customer_entry = tk.Entry(start_window, textvariable=customer_var,
                                    font=self.arabic_font, justify='center', width=25)
            customer_entry.pack(pady=5)
            customer_entry.focus()

            def confirm_start():
                station['active'] = True
                station['start_time'] = datetime.now()
                station['customer_name'] = customer_var.get().strip()
                messagebox.showinfo("تم التشغيل", f"✅ تم تشغيل {station['name']}\n👤 العميل: {station['customer_name'] or 'غير محدد'}")
                start_window.destroy()
                self.refresh_stations()

            # أزرار التحكم
            buttons_frame = tk.Frame(start_window, bg='#f8f9fa')
            buttons_frame.pack(pady=30)

            tk.Button(buttons_frame, text="✅ بدء الجلسة", font=self.button_font,
                     bg=self.colors['success'], fg='white', pady=10,
                     command=confirm_start).pack(side='left', padx=10)

            tk.Button(buttons_frame, text="❌ إلغاء", font=self.button_font,
                     bg=self.colors['secondary'], fg='white', pady=10,
                     command=start_window.destroy).pack(side='left', padx=10)
            
    def create_inventory_tab(self):
        """إنشاء تبويب المخزون المحسن"""
        # الهيدر
        header_frame = tk.Frame(self.inventory_frame, bg='#fd7e14', height=80)
        header_frame.pack(fill='x', padx=10, pady=10)
        header_frame.pack_propagate(False)

        title = tk.Label(header_frame, text="📦 إدارة المخزون والمنتجات",
                        font=('Segoe UI', 18, 'bold'), bg='#fd7e14', fg='white')
        title.pack(pady=25)

        # الإطار الرئيسي
        main_frame = tk.Frame(self.inventory_frame, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg='#f8f9fa')
        buttons_frame.pack(fill='x', padx=10, pady=10)

        add_btn = tk.Button(buttons_frame, text="➕ إضافة منتج جديد", font=self.button_font,
                           bg=self.colors['success'], fg='white', relief='raised', bd=3,
                           cursor='hand2', pady=8)
        add_btn.pack(side='left', padx=5)

        edit_btn = tk.Button(buttons_frame, text="✏️ تعديل منتج", font=self.button_font,
                            bg=self.colors['primary'], fg='white', relief='raised', bd=3,
                            cursor='hand2', pady=8)
        edit_btn.pack(side='left', padx=5)

        delete_btn = tk.Button(buttons_frame, text="🗑️ حذف منتج", font=self.button_font,
                              bg=self.colors['danger'], fg='white', relief='raised', bd=3,
                              cursor='hand2', pady=8)
        delete_btn.pack(side='left', padx=5)

        # جدول المنتجات المحسن
        tree_frame = tk.Frame(main_frame, bg='#f8f9fa')
        tree_frame.pack(fill='both', expand=True, padx=10, pady=10)

        columns = ('ID', 'اسم المنتج', 'السعر', 'الفئة', 'تاريخ الإضافة')
        inventory_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        # تحسين عرض الأعمدة
        column_widths = {'ID': 50, 'اسم المنتج': 200, 'السعر': 100, 'الفئة': 150, 'تاريخ الإضافة': 150}
        for col in columns:
            inventory_tree.heading(col, text=col)
            inventory_tree.column(col, width=column_widths.get(col, 100), anchor='center')

        # إضافة scrollbar
        tree_scroll = ttk.Scrollbar(tree_frame, orient="vertical", command=inventory_tree.yview)
        inventory_tree.configure(yscrollcommand=tree_scroll.set)

        inventory_tree.pack(side='left', fill='both', expand=True)
        tree_scroll.pack(side='right', fill='y')

        # تحميل البيانات
        self.cursor.execute("""
            SELECT p.id, p.name, p.price, c.name, 'اليوم' as date_added
            FROM products p
            JOIN categories c ON p.category_id = c.id
        """)
        products = self.cursor.fetchall()

        for i, product in enumerate(products):
            # تلوين الصفوف بالتناوب
            tag = 'evenrow' if i % 2 == 0 else 'oddrow'
            inventory_tree.insert('', 'end', values=product, tags=(tag,))

        # تكوين ألوان الصفوف
        inventory_tree.tag_configure('evenrow', background='#f8f9fa')
        inventory_tree.tag_configure('oddrow', background='#ffffff')

    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        # الهيدر
        header_frame = tk.Frame(self.reports_frame, bg='#20c997', height=80)
        header_frame.pack(fill='x', padx=10, pady=10)
        header_frame.pack_propagate(False)

        title = tk.Label(header_frame, text="📊 التقارير والإحصائيات",
                        font=('Segoe UI', 18, 'bold'), bg='#20c997', fg='white')
        title.pack(pady=25)

        # الإطار الرئيسي
        main_frame = tk.Frame(self.reports_frame, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # إحصائيات سريعة
        stats_frame = tk.Frame(main_frame, bg='#f8f9fa')
        stats_frame.pack(fill='x', padx=10, pady=10)

        # بطاقات الإحصائيات
        stats_data = [
            {'title': 'مبيعات اليوم', 'value': '1,250 ريال', 'color': '#28a745', 'icon': '💰'},
            {'title': 'عدد الطلبات', 'value': '45 طلب', 'color': '#17a2b8', 'icon': '📋'},
            {'title': 'محطات نشطة', 'value': '3 محطات', 'color': '#dc3545', 'icon': '🎮'},
            {'title': 'إجمالي المنتجات', 'value': '28 منتج', 'color': '#ffc107', 'icon': '📦'}
        ]

        for i, stat in enumerate(stats_data):
            stat_card = tk.Frame(stats_frame, bg=stat['color'], relief='raised', bd=3)
            stat_card.grid(row=0, column=i, padx=10, pady=10, sticky='ew', ipadx=20, ipady=15)

            icon_label = tk.Label(stat_card, text=stat['icon'],
                                 font=('Segoe UI', 24), bg=stat['color'])
            icon_label.pack()

            value_label = tk.Label(stat_card, text=stat['value'],
                                  font=('Segoe UI', 16, 'bold'), bg=stat['color'], fg='white')
            value_label.pack()

            title_label = tk.Label(stat_card, text=stat['title'],
                                  font=('Segoe UI', 12), bg=stat['color'], fg='white')
            title_label.pack()

            stats_frame.grid_columnconfigure(i, weight=1)

        # منطقة الرسوم البيانية (مبسطة)
        charts_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=2)
        charts_frame.pack(fill='both', expand=True, padx=10, pady=10)

        charts_title = tk.Label(charts_frame, text="📈 الرسوم البيانية",
                               font=('Segoe UI', 16, 'bold'), bg='white')
        charts_title.pack(pady=20)

        # رسم بياني مبسط (نص)
        chart_text = tk.Text(charts_frame, height=10, font=('Courier', 12), bg='#f8f9fa')
        chart_text.pack(padx=20, pady=10, fill='both', expand=True)

        # بيانات تجريبية للرسم البياني
        chart_data = """
📊 مبيعات الأسبوع الماضي:

السبت    ████████████████████ 1,200 ريال
الأحد     ███████████████ 900 ريال
الاثنين   ██████████████████████ 1,400 ريال
الثلاثاء  ████████████ 750 ريال
الأربعاء  ███████████████████ 1,100 ريال
الخميس   ████████████████████████ 1,500 ريال
الجمعة   ██████████████████████████ 1,600 ريال

🎮 استخدام محطات البلايستيشن:
محطة 1: ████████████████ 80% من الوقت
محطة 2: ████████████ 60% من الوقت
محطة 3: ██████████████████████ 90% من الوقت
محطة 4: ████████ 40% من الوقت
        """

        chart_text.insert('1.0', chart_data)
        chart_text.config(state='disabled')
            
    def run(self):
        """تشغيل التطبيق"""
        try:
            self.root.mainloop()
        except Exception as e:
            messagebox.showerror("خطأ في النظام", f"حدث خطأ غير متوقع:\n{str(e)}")
            print(f"خطأ في التطبيق: {e}")
        finally:
            # تنظيف الموارد
            if hasattr(self, 'conn') and self.conn:
                self.conn.close()
            print("تم إغلاق النظام بأمان")
        
    def __del__(self):
        """إغلاق قاعدة البيانات"""
        if hasattr(self, 'conn'):
            self.conn.close()

if __name__ == "__main__":
    app = POSApp()
    app.run()
