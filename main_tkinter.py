#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نظام نقاط البيع لمحمد الاشرافى - إصدار محسن مع تصميم جميل
"""

import tkinter as tk
from tkinter import ttk, messagebox, font
import sqlite3
from datetime import datetime
import os
import threading
import time

class POSApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🏪 نظام نقاط البيع - محمد الاشرافى")
        self.root.geometry("1400x900")
        self.root.configure(bg='#2c3e50')
        self.root.state('zoomed')  # ملء الشاشة

        # تكوين الخطوط العربية المحسنة
        self.arabic_font = ('Segoe UI', 11)
        self.title_font = ('Segoe UI', 16, 'bold')
        self.button_font = ('Segoe UI', 12, 'bold')
        self.price_font = ('Segoe UI', 14, 'bold')

        # ألوان التصميم الحديث
        self.colors = {
            'primary': '#3498db',
            'success': '#27ae60',
            'danger': '#e74c3c',
            'warning': '#f39c12',
            'info': '#17a2b8',
            'light': '#f8f9fa',
            'dark': '#2c3e50',
            'secondary': '#6c757d'
        }

        # متغيرات الجلسات النشطة للبلايستيشن
        self.active_sessions = {}
        self.ps_timers = {}
        
        self.setup_database()
        self.create_widgets()
        
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        if not os.path.exists('data'):
            os.makedirs('data')
            
        self.conn = sqlite3.connect('data/pos_database.db')
        self.cursor = self.conn.cursor()
        
        # إنشاء الجداول
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                type TEXT DEFAULT 'cafe'
            )
        ''')
        
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                price REAL NOT NULL,
                category_id INTEGER,
                FOREIGN KEY (category_id) REFERENCES categories (id)
            )
        ''')
        
        # إدراج بيانات تجريبية
        self.cursor.execute("SELECT COUNT(*) FROM categories")
        if self.cursor.fetchone()[0] == 0:
            categories = [
                ('مشروبات ساخنة', 'cafe'),
                ('مشروبات باردة', 'cafe'),
                ('وجبات خفيفة', 'cafe'),
                ('حلويات', 'cafe')
            ]
            self.cursor.executemany("INSERT INTO categories (name, type) VALUES (?, ?)", categories)
            
            products = [
                ('قهوة عربية', 15.0, 1),
                ('شاي أحمر', 10.0, 1),
                ('كابتشينو', 20.0, 1),
                ('عصير برتقال', 12.0, 2),
                ('كولا', 8.0, 2),
                ('ساندويش تونة', 25.0, 3),
                ('كيك شوكولاتة', 18.0, 4)
            ]
            self.cursor.executemany("INSERT INTO products (name, price, category_id) VALUES (?, ?, ?)", products)
            
        self.conn.commit()
        
    def create_widgets(self):
        """إنشاء واجهة المستخدم المحسنة"""
        # الشريط العلوي الجميل مع تدرج
        header_frame = tk.Frame(self.root, bg='#34495e', height=100, relief='raised', bd=3)
        header_frame.pack(fill='x', padx=0, pady=0)
        header_frame.pack_propagate(False)

        # عنوان جميل مع الوقت
        title_frame = tk.Frame(header_frame, bg='#34495e')
        title_frame.pack(fill='both', expand=True)

        title_label = tk.Label(title_frame, text="🏪 نظام نقاط البيع - محمد الاشرافى",
                              font=('Segoe UI', 20, 'bold'), bg='#34495e', fg='#ecf0f1')
        title_label.pack(side='left', padx=20, pady=25)

        # عرض الوقت الحالي
        self.time_label = tk.Label(title_frame, text="",
                                  font=('Segoe UI', 14), bg='#34495e', fg='#bdc3c7')
        self.time_label.pack(side='right', padx=20, pady=25)
        self.update_time()

        # الإطار الرئيسي مع تصميم حديث
        main_frame = tk.Frame(self.root, bg='#ecf0f1', relief='sunken', bd=2)
        main_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # تكوين التبويبات مع تصميم محسن
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TNotebook', background='#ecf0f1', borderwidth=0)
        style.configure('TNotebook.Tab', padding=[20, 10], font=self.button_font)

        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)

        # تبويب المقهى مع تصميم جميل
        self.cafe_frame = tk.Frame(notebook, bg='#ffffff', relief='raised', bd=2)
        notebook.add(self.cafe_frame, text='☕ المقهى')
        self.create_cafe_tab()

        # تبويب البلايستيشن مع تصميم جميل
        self.ps_frame = tk.Frame(notebook, bg='#ffffff', relief='raised', bd=2)
        notebook.add(self.ps_frame, text='🎮 البلايستيشن')
        self.create_playstation_tab()

        # تبويب المخزون مع تصميم جميل
        self.inventory_frame = tk.Frame(notebook, bg='#ffffff', relief='raised', bd=2)
        notebook.add(self.inventory_frame, text='📦 المخزون')
        self.create_inventory_tab()

        # تبويب التقارير
        self.reports_frame = tk.Frame(notebook, bg='#ffffff', relief='raised', bd=2)
        notebook.add(self.reports_frame, text='📊 التقارير')
        self.create_reports_tab()

    def update_time(self):
        """تحديث الوقت الحالي"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=f"🕐 {current_time}")
        self.root.after(1000, self.update_time)
        
    def create_cafe_tab(self):
        """إنشاء تبويب المقهى المحسن"""
        # الإطار الأيسر - المنتجات مع تصميم جميل
        left_frame = tk.Frame(self.cafe_frame, bg='#f8f9fa', relief='raised', bd=3)
        left_frame.pack(side='left', fill='both', expand=True, padx=10, pady=10)

        # عنوان المنتجات مع تصميم جميل
        products_header = tk.Frame(left_frame, bg=self.colors['primary'], height=60)
        products_header.pack(fill='x', padx=5, pady=5)
        products_header.pack_propagate(False)

        products_title = tk.Label(products_header, text="🍽️ قائمة المنتجات",
                                 font=('Segoe UI', 18, 'bold'), bg=self.colors['primary'], fg='white')
        products_title.pack(pady=15)

        # إطار الفئات مع تصميم محسن
        categories_frame = tk.Frame(left_frame, bg='#f8f9fa')
        categories_frame.pack(fill='x', padx=10, pady=10)

        # أزرار الفئات الجميلة
        self.cursor.execute("SELECT * FROM categories WHERE type='cafe'")
        categories = self.cursor.fetchall()

        category_colors = [self.colors['success'], self.colors['info'], self.colors['warning'], self.colors['danger']]

        for i, category in enumerate(categories):
            color = category_colors[i % len(category_colors)]
            btn = tk.Button(categories_frame, text=f"📂 {category[1]}", font=self.button_font,
                           bg=color, fg='white', relief='raised', bd=3,
                           activebackground=color, activeforeground='white',
                           cursor='hand2', pady=10,
                           command=lambda c=category[0]: self.load_products(c))
            btn.grid(row=0, column=i, padx=5, pady=5, sticky='ew')
            categories_frame.grid_columnconfigure(i, weight=1)

        # إطار المنتجات مع خلفية جميلة
        products_container = tk.Frame(left_frame, bg='#f8f9fa')
        products_container.pack(fill='both', expand=True, padx=10, pady=5)

        # إضافة scrollbar للمنتجات
        canvas = tk.Canvas(products_container, bg='#ffffff', highlightthickness=0)
        scrollbar = ttk.Scrollbar(products_container, orient="vertical", command=canvas.yview)
        self.products_frame = tk.Frame(canvas, bg='#ffffff')

        self.products_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.products_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # الإطار الأيمن - الطلب مع تصميم جميل
        right_frame = tk.Frame(self.cafe_frame, bg='#e8f4fd', relief='raised', bd=3, width=450)
        right_frame.pack(side='right', fill='y', padx=10, pady=10)
        right_frame.pack_propagate(False)

        # عنوان الطلب مع تصميم جميل
        order_header = tk.Frame(right_frame, bg=self.colors['info'], height=60)
        order_header.pack(fill='x', padx=5, pady=5)
        order_header.pack_propagate(False)

        order_title = tk.Label(order_header, text="🛒 الطلب الحالي",
                              font=('Segoe UI', 16, 'bold'), bg=self.colors['info'], fg='white')
        order_title.pack(pady=15)

        # جدول الطلب مع تصميم محسن
        tree_frame = tk.Frame(right_frame, bg='#e8f4fd')
        tree_frame.pack(fill='both', expand=True, padx=10, pady=5)

        columns = ('المنتج', 'الكمية', 'السعر', 'المجموع')
        self.order_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=12)

        # تحسين عرض الأعمدة
        column_widths = {'المنتج': 120, 'الكمية': 60, 'السعر': 80, 'المجموع': 90}
        for col in columns:
            self.order_tree.heading(col, text=col)
            self.order_tree.column(col, width=column_widths[col], anchor='center')

        # إضافة scrollbar للجدول
        tree_scroll = ttk.Scrollbar(tree_frame, orient="vertical", command=self.order_tree.yview)
        self.order_tree.configure(yscrollcommand=tree_scroll.set)

        self.order_tree.pack(side='left', fill='both', expand=True)
        tree_scroll.pack(side='right', fill='y')

        # إطار المجاميع مع تصميم جميل
        totals_frame = tk.Frame(right_frame, bg='#d4edda', relief='raised', bd=2)
        totals_frame.pack(fill='x', padx=10, pady=10)

        self.total_label = tk.Label(totals_frame, text="💰 المجموع: 0.00 ريال",
                                   font=('Segoe UI', 16, 'bold'), bg='#d4edda', fg='#155724')
        self.total_label.pack(pady=15)

        # أزرار الإجراءات مع تصميم جميل
        actions_frame = tk.Frame(right_frame, bg='#e8f4fd')
        actions_frame.pack(fill='x', padx=10, pady=10)

        pay_btn = tk.Button(actions_frame, text="💳 الدفع والطباعة", font=self.button_font,
                           bg=self.colors['success'], fg='white', relief='raised', bd=4,
                           activebackground='#1e7e34', activeforeground='white',
                           cursor='hand2', pady=12,
                           command=self.process_payment)
        pay_btn.pack(fill='x', pady=3)

        discount_btn = tk.Button(actions_frame, text="🏷️ تطبيق خصم", font=self.button_font,
                                bg=self.colors['warning'], fg='white', relief='raised', bd=4,
                                activebackground='#d39e00', activeforeground='white',
                                cursor='hand2', pady=12,
                                command=self.apply_discount)
        discount_btn.pack(fill='x', pady=3)

        clear_btn = tk.Button(actions_frame, text="🗑️ مسح الطلب", font=self.button_font,
                             bg=self.colors['danger'], fg='white', relief='raised', bd=4,
                             activebackground='#bd2130', activeforeground='white',
                             cursor='hand2', pady=12,
                             command=self.clear_order)
        clear_btn.pack(fill='x', pady=3)
        
        # تحميل المنتجات الافتراضية
        if categories:
            self.load_products(categories[0][0])
            
        # متغيرات الطلب
        self.current_order = []
        self.order_total = 0.0
        
    def load_products(self, category_id):
        """تحميل المنتجات حسب الفئة"""
        # مسح المنتجات الحالية
        for widget in self.products_frame.winfo_children():
            widget.destroy()
            
        # تحميل المنتجات الجديدة
        self.cursor.execute("SELECT * FROM products WHERE category_id=?", (category_id,))
        products = self.cursor.fetchall()
        
        row, col = 0, 0
        for product in products:
            product_btn = tk.Button(self.products_frame, 
                                   text=f"{product[1]}\n{product[2]:.2f} ريال",
                                   font=self.arabic_font, bg='#f39c12', fg='white',
                                   relief='raised', bd=3, width=15, height=4,
                                   command=lambda p=product: self.add_to_order(p))
            product_btn.grid(row=row, column=col, padx=5, pady=5, sticky='ew')
            
            col += 1
            if col >= 3:
                col = 0
                row += 1
                
        # تكوين الأعمدة
        for i in range(3):
            self.products_frame.grid_columnconfigure(i, weight=1)
            
    def add_to_order(self, product):
        """إضافة منتج للطلب"""
        # البحث عن المنتج في الطلب الحالي
        for item in self.current_order:
            if item['id'] == product[0]:
                item['quantity'] += 1
                item['total'] = item['quantity'] * item['price']
                break
        else:
            # إضافة منتج جديد
            self.current_order.append({
                'id': product[0],
                'name': product[1],
                'price': product[2],
                'quantity': 1,
                'total': product[2]
            })
        
        self.update_order_display()
        
    def update_order_display(self):
        """تحديث عرض الطلب"""
        # مسح الجدول
        for item in self.order_tree.get_children():
            self.order_tree.delete(item)
            
        # إضافة العناصر
        self.order_total = 0.0
        for item in self.current_order:
            self.order_tree.insert('', 'end', values=(
                item['name'], item['quantity'], 
                f"{item['price']:.2f}", f"{item['total']:.2f}"
            ))
            self.order_total += item['total']
            
        # تحديث المجموع
        self.total_label.config(text=f"المجموع: {self.order_total:.2f} ريال")
        
    def process_payment(self):
        """معالجة الدفع"""
        if not self.current_order:
            messagebox.showwarning("تحذير", "لا توجد عناصر في الطلب")
            return
            
        messagebox.showinfo("نجح", f"تم الدفع بنجاح\nالمبلغ: {self.order_total:.2f} ريال")
        self.clear_order()
        
    def clear_order(self):
        """مسح الطلب"""
        self.current_order = []
        self.order_total = 0.0
        self.update_order_display()
        
    def create_playstation_tab(self):
        """إنشاء تبويب البلايستيشن"""
        # عنوان
        title = tk.Label(self.ps_frame, text="🎮 إدارة محطات البلايستيشن", 
                        font=self.title_font, bg='white')
        title.pack(pady=20)
        
        # إطار المحطات
        stations_frame = tk.Frame(self.ps_frame, bg='white')
        stations_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # محطات تجريبية
        stations = [
            {'name': 'محطة 1', 'status': 'متاح', 'color': '#27ae60'},
            {'name': 'محطة 2', 'status': 'نشط - 45 دقيقة', 'color': '#e74c3c'},
            {'name': 'محطة 3', 'status': 'متاح', 'color': '#27ae60'},
            {'name': 'محطة 4', 'status': 'نشط - 1:20 ساعة', 'color': '#e74c3c'}
        ]
        
        row, col = 0, 0
        for station in stations:
            station_frame = tk.Frame(stations_frame, bg=station['color'], relief='raised', bd=3)
            station_frame.grid(row=row, column=col, padx=10, pady=10, sticky='ew')
            
            name_label = tk.Label(station_frame, text=station['name'], 
                                 font=self.title_font, bg=station['color'], fg='white')
            name_label.pack(pady=10)
            
            status_label = tk.Label(station_frame, text=station['status'], 
                                   font=self.arabic_font, bg=station['color'], fg='white')
            status_label.pack(pady=5)
            
            col += 1
            if col >= 2:
                col = 0
                row += 1
                
        # تكوين الأعمدة
        for i in range(2):
            stations_frame.grid_columnconfigure(i, weight=1)
            
    def create_inventory_tab(self):
        """إنشاء تبويب المخزون"""
        title = tk.Label(self.inventory_frame, text="📦 إدارة المخزون", 
                        font=self.title_font, bg='white')
        title.pack(pady=20)
        
        # جدول المنتجات
        columns = ('ID', 'اسم المنتج', 'السعر', 'الفئة')
        inventory_tree = ttk.Treeview(self.inventory_frame, columns=columns, show='headings')
        
        for col in columns:
            inventory_tree.heading(col, text=col)
            inventory_tree.column(col, width=150)
        
        inventory_tree.pack(padx=20, pady=10, fill='both', expand=True)
        
        # تحميل البيانات
        self.cursor.execute("""
            SELECT p.id, p.name, p.price, c.name 
            FROM products p 
            JOIN categories c ON p.category_id = c.id
        """)
        products = self.cursor.fetchall()
        
        for product in products:
            inventory_tree.insert('', 'end', values=product)
            
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()
        
    def __del__(self):
        """إغلاق قاعدة البيانات"""
        if hasattr(self, 'conn'):
            self.conn.close()

if __name__ == "__main__":
    app = POSApp()
    app.run()
