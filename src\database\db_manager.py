#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Database Manager for محمد الاشرافى POS System
Handles all database operations and provides an interface for the application
"""

import sqlite3
import os
import datetime
import json
from pathlib import Path

class DatabaseManager:
    """Database manager class for the POS system"""

    def __init__(self, db_file="pos_data.db"):
        """Initialize database manager with the specified database file"""
        # Get the directory of the database file
        base_dir = os.path.dirname(os.path.abspath(__file__))
        db_path = os.path.join(os.path.dirname(os.path.dirname(base_dir)), "data")

        # Create data directory if it doesn't exist
        Path(db_path).mkdir(parents=True, exist_ok=True)

        self.db_file = os.path.join(db_path, db_file)
        self.connection = None
        self.cursor = None

    def connect(self):
        """Connect to the SQLite database"""
        try:
            self.connection = sqlite3.connect(self.db_file)
            self.connection.row_factory = sqlite3.Row  # Return rows as dictionaries
            self.cursor = self.connection.cursor()
            return True
        except sqlite3.Error as e:
            print(f"Database connection error: {e}")
            return False

    def disconnect(self):
        """Close the database connection"""
        if self.connection:
            self.connection.close()
            self.connection = None
            self.cursor = None

    def initialize_database(self):
        """Create all required tables if they don't exist"""
        if not self.connect():
            return False

        try:
            # Users table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password TEXT NOT NULL,
                    full_name TEXT,
                    role TEXT NOT NULL,
                    last_login TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active INTEGER DEFAULT 1
                )
            """)

            # Categories table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    type TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Products table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    category_id INTEGER,
                    price REAL NOT NULL,
                    cost REAL DEFAULT 0,
                    description TEXT,
                    image_path TEXT,
                    barcode TEXT,
                    is_available INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES categories (id)
                )
            """)

            # Inventory table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS inventory (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id INTEGER,
                    quantity INTEGER DEFAULT 0,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            """)

            # PlayStation stations table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS playstation_stations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    station_number INTEGER UNIQUE NOT NULL,
                    station_type TEXT NOT NULL,
                    hourly_rate REAL NOT NULL,
                    is_available INTEGER DEFAULT 1,
                    status TEXT DEFAULT 'idle',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # PlayStation sessions table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS playstation_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    station_id INTEGER NOT NULL,
                    customer_name TEXT,
                    start_time TIMESTAMP,
                    end_time TIMESTAMP,
                    duration INTEGER,
                    amount REAL,
                    status TEXT DEFAULT 'active',
                    notes TEXT,
                    FOREIGN KEY (station_id) REFERENCES playstation_stations (id)
                )
            """)

            # Orders table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_number TEXT UNIQUE NOT NULL,
                    customer_name TEXT,
                    order_type TEXT NOT NULL,
                    total_amount REAL NOT NULL,
                    discount_amount REAL DEFAULT 0,
                    tax_amount REAL DEFAULT 0,
                    final_amount REAL NOT NULL,
                    payment_method TEXT NOT NULL,
                    order_status TEXT NOT NULL,
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            """)

            # Order items table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS order_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_id INTEGER NOT NULL,
                    product_id INTEGER NOT NULL,
                    quantity INTEGER NOT NULL,
                    unit_price REAL NOT NULL,
                    total_price REAL NOT NULL,
                    notes TEXT,
                    FOREIGN KEY (order_id) REFERENCES orders (id),
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            """)

            # Customers table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS customers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT,
                    email TEXT,
                    address TEXT,
                    balance REAL DEFAULT 0,
                    last_visit TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Expenses table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS expenses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    expense_type TEXT NOT NULL,
                    amount REAL NOT NULL,
                    description TEXT,
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            """)

            # Transactions table for financial tracking
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    transaction_type TEXT NOT NULL,
                    reference_id INTEGER,
                    reference_type TEXT,
                    amount REAL NOT NULL,
                    payment_method TEXT,
                    notes TEXT,
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            """)

            # Settings table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS settings (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Create an admin user if none exists
            self.cursor.execute("SELECT COUNT(*) FROM users")
            if self.cursor.fetchone()[0] == 0:
                self.cursor.execute(
                    "INSERT INTO users (username, password, full_name, role) VALUES (?, ?, ?, ?)",
                    ("admin", "admin123", "System Administrator", "admin")
                )

            # Add some default categories
            default_categories = [
                ("مشروبات ساخنة", "قهوة وشاي وغيرها", "cafe"),
                ("مشروبات باردة", "عصائر ومشروبات غازية", "cafe"),
                ("حلويات", "كعك وحلويات متنوعة", "cafe"),
                ("وجبات خفيفة", "ساندويتش وسناكس", "cafe"),
                ("PlayStation 4", "محطات بلايستيشن 4", "playstation"),
                ("PlayStation 5", "محطات بلايستيشن 5", "playstation")
            ]

            self.cursor.execute("SELECT COUNT(*) FROM categories")
            if self.cursor.fetchone()[0] == 0:
                self.cursor.executemany(
                    "INSERT INTO categories (name, description, type) VALUES (?, ?, ?)",
                    default_categories
                )

            # Add some default settings
            default_settings = [
                ("store_name", "محمد الاشرافى"),
                ("store_address", ""),
                ("store_phone", ""),
                ("tax_rate", "0"),
                ("receipt_footer", "شكرا لزيارتكم"),
                ("currency", "ريال"),
                ("language", "ar"),
                ("theme", "dark")
            ]

            self.cursor.execute("SELECT COUNT(*) FROM settings")
            if self.cursor.fetchone()[0] == 0:
                self.cursor.executemany(
                    "INSERT INTO settings (key, value) VALUES (?, ?)",
                    default_settings
                )

            self.connection.commit()
            return True

        except sqlite3.Error as e:
            print(f"Database initialization error: {e}")
            return False
        finally:
            self.disconnect()

    # User management methods
    def authenticate_user(self, username, password):
        """Authenticate a user with the given credentials"""
        if not self.connect():
            return None

        try:
            self.cursor.execute(
                "SELECT * FROM users WHERE username = ? AND password = ? AND is_active = 1",
                (username, password)
            )
            user = self.cursor.fetchone()

            if user:
                # Update last login time
                self.cursor.execute(
                    "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?",
                    (user["id"],)
                )
                self.connection.commit()
                return dict(user)
            return None
        except sqlite3.Error as e:
            print(f"Authentication error: {e}")
            return None
        finally:
            self.disconnect()

    def add_user(self, username, password, full_name, role):
        """Add a new user"""
        if not self.connect():
            return False

        try:
            self.cursor.execute(
                "INSERT INTO users (username, password, full_name, role) VALUES (?, ?, ?, ?)",
                (username, password, full_name, role)
            )
            self.connection.commit()
            return True
        except sqlite3.Error as e:
            print(f"Add user error: {e}")
            return False
        finally:
            self.disconnect()

    def get_all_users(self):
        """Get all users"""
        if not self.connect():
            return []

        try:
            self.cursor.execute("SELECT * FROM users")
            users = self.cursor.fetchall()
            return [dict(user) for user in users]
        except sqlite3.Error as e:
            print(f"Get users error: {e}")
            return []
        finally:
            self.disconnect()

    # Category management methods
    def add_category(self, name, description=None, category_type="cafe"):
        """Add a new category"""
        if not self.connect():
            return False

        try:
            self.cursor.execute(
                "INSERT INTO categories (name, description, type) VALUES (?, ?, ?)",
                (name, description, category_type)
            )
            self.connection.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"Add category error: {e}")
            return False
        finally:
            self.disconnect()

    def get_all_categories(self, category_type=None):
        """Get all categories, optionally filtered by type"""
        if not self.connect():
            return []

        try:
            if category_type:
                self.cursor.execute("SELECT * FROM categories WHERE type = ? ORDER BY name", (category_type,))
            else:
                self.cursor.execute("SELECT * FROM categories ORDER BY name")

            categories = self.cursor.fetchall()
            return [dict(category) for category in categories]
        except sqlite3.Error as e:
            print(f"Get categories error: {e}")
            return []
        finally:
            self.disconnect()

    def get_category(self, category_id):
        """Get a category by ID"""
        if not self.connect():
            return None

        try:
            self.cursor.execute("SELECT * FROM categories WHERE id = ?", (category_id,))
            category = self.cursor.fetchone()
            return dict(category) if category else None
        except sqlite3.Error as e:
            print(f"Get category error: {e}")
            return None
        finally:
            self.disconnect()

    def update_category(self, category_id, name=None, description=None):
        """Update a category"""
        if not self.connect():
            return False

        try:
            fields = []
            values = []

            if name is not None:
                fields.append("name = ?")
                values.append(name)
            if description is not None:
                fields.append("description = ?")
                values.append(description)

            if not fields:
                return False

            values.append(category_id)
            query = f"UPDATE categories SET {', '.join(fields)} WHERE id = ?"

            self.cursor.execute(query, values)
            self.connection.commit()
            return True
        except sqlite3.Error as e:
            print(f"Update category error: {e}")
            return False
        finally:
            self.disconnect()

    def delete_category(self, category_id):
        """Delete a category"""
        if not self.connect():
            return False

        try:
            # Check if category has products
            self.cursor.execute("SELECT COUNT(*) FROM products WHERE category_id = ?", (category_id,))
            if self.cursor.fetchone()[0] > 0:
                return False  # Cannot delete category with products

            self.cursor.execute("DELETE FROM categories WHERE id = ?", (category_id,))
            self.connection.commit()
            return True
        except sqlite3.Error as e:
            print(f"Delete category error: {e}")
            return False
        finally:
            self.disconnect()

    # Product management methods
    def add_product(self, name, category_id, price, cost=0, description=None, image_path=None, barcode=None):
        """Add a new product"""
        if not self.connect():
            return False

        try:
            self.cursor.execute(
                """INSERT INTO products 
                   (name, category_id, price, cost, description, image_path, barcode) 
                   VALUES (?, ?, ?, ?, ?, ?, ?)""",
                (name, category_id, price, cost, description, image_path, barcode)
            )
            product_id = self.cursor.lastrowid

            # Initialize inventory for the product
            self.cursor.execute(
                "INSERT INTO inventory (product_id, quantity) VALUES (?, ?)",
                (product_id, 0)
            )

            self.connection.commit()
            return product_id
        except sqlite3.Error as e:
            print(f"Add product error: {e}")
            return False
        finally:
            self.disconnect()

    def get_product(self, product_id):
        """Get a product by ID"""
        if not self.connect():
            return None

        try:
            self.cursor.execute("""
                SELECT p.*, c.name as category_name, i.quantity
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN inventory i ON p.id = i.product_id
                WHERE p.id = ?
            """, (product_id,))

            product = self.cursor.fetchone()
            return dict(product) if product else None
        except sqlite3.Error as e:
            print(f"Get product error: {e}")
            return None
        finally:
            self.disconnect()

    def get_all_products(self, category_id=None):
        """Get all products, optionally filtered by category"""
        if not self.connect():
            return []

        try:
            query = """
                SELECT p.*, c.name as category_name, i.quantity
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN inventory i ON p.id = i.product_id
            """

            if category_id:
                query += " WHERE p.category_id = ?"
                self.cursor.execute(query, (category_id,))
            else:
                self.cursor.execute(query)

            products = self.cursor.fetchall()
            return [dict(product) for product in products]
        except sqlite3.Error as e:
            print(f"Get products error: {e}")
            return []
        finally:
            self.disconnect()

    def update_product(self, product_id, **kwargs):
        """Update a product"""
        if not self.connect():
            return False

        try:
            fields = []
            values = []

            for key, value in kwargs.items():
                if key in ['name', 'category_id', 'price', 'cost', 'description', 'image_path', 'barcode', 'is_available']:
                    fields.append(f"{key} = ?")
                    values.append(value)

            if not fields:
                return False

            values.append(product_id)
            query = f"UPDATE products SET {', '.join(fields)} WHERE id = ?"

            self.cursor.execute(query, values)
            self.connection.commit()
            return True
        except sqlite3.Error as e:
            print(f"Update product error: {e}")
            return False
        finally:
            self.disconnect()

    def delete_product(self, product_id):
        """Delete a product"""
        if not self.connect():
            return False

        try:
            # Delete from inventory first
            self.cursor.execute("DELETE FROM inventory WHERE product_id = ?", (product_id,))
            # Then delete the product
            self.cursor.execute("DELETE FROM products WHERE id = ?", (product_id,))
            self.connection.commit()
            return True
        except sqlite3.Error as e:
            print(f"Delete product error: {e}")
            return False
        finally:
            self.disconnect()

    # Inventory management methods
    def update_inventory(self, product_id, quantity):
        """Update inventory quantity for a product"""
        if not self.connect():
            return False

        try:
            self.cursor.execute(
                "UPDATE inventory SET quantity = ?, last_updated = CURRENT_TIMESTAMP WHERE product_id = ?",
                (quantity, product_id)
            )
            self.connection.commit()
            return True
        except sqlite3.Error as e:
            print(f"Update inventory error: {e}")
            return False
        finally:
            self.disconnect()

    # PlayStation station management methods
    def add_playstation_station(self, station_number, station_type, hourly_rate):
        """Add a new PlayStation station"""
        if not self.connect():
            return False

        try:
            self.cursor.execute(
                "INSERT INTO playstation_stations (station_number, station_type, hourly_rate) VALUES (?, ?, ?)",
                (station_number, station_type, hourly_rate)
            )
            self.connection.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"Add PlayStation station error: {e}")
            return False
        finally:
            self.disconnect()

    def get_all_playstation_stations(self):
        """Get all PlayStation stations"""
        if not self.connect():
            return []

        try:
            self.cursor.execute("SELECT * FROM playstation_stations ORDER BY station_number")
            stations = self.cursor.fetchall()
            return [dict(station) for station in stations]
        except sqlite3.Error as e:
            print(f"Get PlayStation stations error: {e}")
            return []
        finally:
            self.disconnect()

    def start_playstation_session(self, station_id, customer_name=None, notes=None):
        """Start a new PlayStation session"""
        if not self.connect():
            return False

        try:
            # Check if station is available
            self.cursor.execute(
                "SELECT * FROM playstation_stations WHERE id = ? AND is_available = 1",
                (station_id,)
            )

            if not self.cursor.fetchone():
                return False

            # Update station status
            self.cursor.execute(
                "UPDATE playstation_stations SET status = 'active', is_available = 0 WHERE id = ?",
                (station_id,)
            )

            # Create a new session
            self.cursor.execute(
                """INSERT INTO playstation_sessions 
                   (station_id, customer_name, start_time, status, notes) 
                   VALUES (?, ?, CURRENT_TIMESTAMP, 'active', ?)""",
                (station_id, customer_name, notes)
            )

            self.connection.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"Start PlayStation session error: {e}")
            return False
        finally:
            self.disconnect()

    def end_playstation_session(self, session_id):
        """End a PlayStation session and calculate the bill"""
        if not self.connect():
            return False

        try:
            # Get session details
            self.cursor.execute(
                """SELECT ps.*, pst.hourly_rate, pst.id as station_id 
                   FROM playstation_sessions ps
                   JOIN playstation_stations pst ON ps.station_id = pst.id
                   WHERE ps.id = ? AND ps.status = 'active'""",
                (session_id,)
            )

            session = self.cursor.fetchone()
            if not session:
                return False

            # Calculate duration and amount
            start_time = datetime.datetime.strptime(session["start_time"], "%Y-%m-%d %H:%M:%S")
            end_time = datetime.datetime.now()
            duration_seconds = (end_time - start_time).total_seconds()
            duration_hours = duration_seconds / 3600
            amount = round(duration_hours * session["hourly_rate"], 2)

            # Update session
            self.cursor.execute(
                """UPDATE playstation_sessions
                   SET end_time = CURRENT_TIMESTAMP,
                       duration = ?,
                       amount = ?,
                       status = 'completed'
                   WHERE id = ?""",
                (int(duration_seconds), amount, session_id)
            )

            # Update station status
            self.cursor.execute(
                "UPDATE playstation_stations SET status = 'idle', is_available = 1 WHERE id = ?",
                (session["station_id"],)
            )

            self.connection.commit()

            # Return session details for billing
            return {
                'session_id': session_id,
                'station_id': session["station_id"],
                'start_time': start_time.strftime("%Y-%m-%d %H:%M:%S"),
                'end_time': end_time.strftime("%Y-%m-%d %H:%M:%S"),
                'duration_hours': round(duration_hours, 2),
                'hourly_rate': session["hourly_rate"],
                'amount': amount
            }

        except sqlite3.Error as e:
            print(f"End PlayStation session error: {e}")
            return False
        finally:
            self.disconnect()

    # Order management methods
    def create_order(self, customer_name, order_type, payment_method, user_id, items):
        """Create a new order with items"""
        if not self.connect():
            return False

        try:
            # Generate order number
            order_date = datetime.datetime.now().strftime("%Y%m%d")
            self.cursor.execute("SELECT COUNT(*) FROM orders WHERE order_number LIKE ?", (f"{order_date}%",))
            count = self.cursor.fetchone()[0] + 1
            order_number = f"{order_date}{count:04d}"

            # Calculate totals
            total_amount = sum(item["quantity"] * item["price"] for item in items)
            discount_amount = 0  # Can be parameterized later
            tax_amount = 0  # Can be calculated based on settings
            final_amount = total_amount - discount_amount + tax_amount

            # Create order
            self.cursor.execute(
                """INSERT INTO orders 
                   (order_number, customer_name, order_type, total_amount,
                    discount_amount, tax_amount, final_amount,
                    payment_method, order_status, created_by)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (order_number, customer_name, order_type, total_amount,
                 discount_amount, tax_amount, final_amount,
                 payment_method, "completed", user_id)
            )

            order_id = self.cursor.lastrowid

            # Add order items
            for item in items:
                self.cursor.execute(
                    """INSERT INTO order_items
                       (order_id, product_id, quantity, unit_price, total_price, notes)
                       VALUES (?, ?, ?, ?, ?, ?)""",
                    (order_id, item["product_id"], item["quantity"], 
                     item["price"], item["quantity"] * item["price"], 
                     item.get("notes", None))
                )

                # Update inventory
                self.cursor.execute(
                    """UPDATE inventory 
                       SET quantity = quantity - ?,
                           last_updated = CURRENT_TIMESTAMP
                       WHERE product_id = ?""",
                    (item["quantity"], item["product_id"])
                )

            # Record transaction
            self.cursor.execute(
                """INSERT INTO transactions
                   (transaction_type, reference_id, reference_type,
                    amount, payment_method, created_by)
                   VALUES (?, ?, ?, ?, ?, ?)""",
                ("sale", order_id, "order", final_amount, payment_method, user_id)
            )

            self.connection.commit()
            return {
                "order_id": order_id,
                "order_number": order_number,
                "final_amount": final_amount
            }
        except sqlite3.Error as e:
            print(f"Create order error: {e}")
            return False
        finally:
            self.disconnect()
            
    def get_order(self, order_id):
        """Get an order by ID with all its items"""
        if not self.connect():
            return None
            
        try:
            # Get order details
            self.cursor.execute("""
                SELECT o.*, u.username as created_by_user
                FROM orders o
                LEFT JOIN users u ON o.created_by = u.id
                WHERE o.id = ?
            """, (order_id,))
            
            order = self.cursor.fetchone()
            if not order:
                return None
                
            # Get order items
            self.cursor.execute("""
                SELECT oi.*, p.name as product_name
                FROM order_items oi
                JOIN products p ON oi.product_id = p.id
                WHERE oi.order_id = ?
            """, (order_id,))
            
            items = self.cursor.fetchall()
            
            result = dict(order)
            result['items'] = [dict(item) for item in items]
            return result
            
        except sqlite3.Error as e:
            print(f"Get order error: {e}")
            return None
        finally:
            self.disconnect()
            
    def get_all_orders(self, start_date=None, end_date=None, limit=100):
        """Get all orders with optional date filtering"""
        if not self.connect():
            return []
            
        try:
            query = """
                SELECT o.*, u.username as created_by_user
                FROM orders o
                LEFT JOIN users u ON o.created_by = u.id
            """
            
            params = []
            
            if start_date and end_date:
                query += " WHERE o.created_at BETWEEN ? AND ?"
                params.extend([start_date, end_date])
            elif start_date:
                query += " WHERE o.created_at >= ?"
                params.append(start_date)
            elif end_date:
                query += " WHERE o.created_at <= ?"
                params.append(end_date)
                
            query += " ORDER BY o.created_at DESC LIMIT ?"
            params.append(limit)
            
            self.cursor.execute(query, params)
            orders = self.cursor.fetchall()
            return [dict(order) for order in orders]
            
        except sqlite3.Error as e:
            print(f"Get all orders error: {e}")
            return []
        finally:
            self.disconnect()
            
    def update_order_status(self, order_id, status):
        """Update an order's status"""
        if not self.connect():
            return False
            
        try:
            self.cursor.execute(
                "UPDATE orders SET order_status = ? WHERE id = ?",
                (status, order_id)
            )
            self.connection.commit()
            return True
        except sqlite3.Error as e:
            print(f"Update order status error: {e}")
            return False
        finally:
            self.disconnect()
            
    def void_order(self, order_id, reason, user_id):
        """Void an order and return inventory items"""
        if not self.connect():
            return False
            
        try:
            # Check if order exists and is not already voided
            self.cursor.execute(
                "SELECT order_status FROM orders WHERE id = ?",
                (order_id,)
            )
            
            result = self.cursor.fetchone()
            if not result or result["order_status"] == "voided":
                return False
                
            # Get order items to restore inventory
            self.cursor.execute(
                "SELECT product_id, quantity FROM order_items WHERE order_id = ?",
                (order_id,)
            )
            
            items = self.cursor.fetchall()
            
            # Update order status
            self.cursor.execute(
                "UPDATE orders SET order_status = 'voided' WHERE id = ?",
                (order_id,)
            )
            
            # Restore inventory
            for item in items:
                self.cursor.execute(
                    """UPDATE inventory 
                       SET quantity = quantity + ?,
                           last_updated = CURRENT_TIMESTAMP
                       WHERE product_id = ?""",
                    (item["quantity"], item["product_id"])
                )
                
            # Record transaction for void
            self.cursor.execute(
                """INSERT INTO transactions
                   (transaction_type, reference_id, reference_type,
                    amount, notes, created_by)
                   VALUES (?, ?, ?, ?, ?, ?)""",
                ("void", order_id, "order", 0, reason, user_id)
            )
            
            self.connection.commit()
            return True
            
        except sqlite3.Error as e:
            print(f"Void order error: {e}")
            return False
        finally:
            self.disconnect()
    def get_settings(self, key=None):
        """Get system settings, either all or a specific key"""
        if not self.connect():
            return {} if key else []

        try:
            if key:
                self.cursor.execute("SELECT * FROM settings WHERE key = ?", (key,))
                setting = self.cursor.fetchone()
                return dict(setting) if setting else {}
            else:
                self.cursor.execute("SELECT * FROM settings")
                settings = self.cursor.fetchall()
                return {s["key"]: s["value"] for s in settings}
        except sqlite3.Error as e:
            print(f"Get settings error: {e}")
            return {} if key else []
        finally:
            self.disconnect()

    def update_setting(self, key, value):
        """Update a setting value"""
        if not self.connect():
            return False

        try:
            self.cursor.execute(
                """INSERT OR REPLACE INTO settings (key, value, updated_at)
                   VALUES (?, ?, CURRENT_TIMESTAMP)""",
                (key, value)
            )
            self.connection.commit()
            return True
        except sqlite3.Error as e:
            print(f"Update setting error: {e}")
            return False
        finally:
            self.disconnect()
            
    def backup_settings(self, backup_path):
        """Export all settings to a JSON file"""
        settings = self.get_settings()
        if not settings:
            return False
            
        try:
            with open(backup_path, 'w') as f:
                json.dump(settings, f, indent=4)
            return True
        except Exception as e:
            print(f"Settings backup error: {e}")
            return False
            
    def restore_settings(self, backup_path):
        """Import settings from a JSON file"""
        if not self.connect():
            return False
            
        try:
            with open(backup_path, 'r') as f:
                settings = json.load(f)
                
            for key, value in settings.items():
                self.update_setting(key, value)
                
            return True
        except Exception as e:
            print(f"Settings restore error: {e}")
            return False
        finally:
            self.disconnect()