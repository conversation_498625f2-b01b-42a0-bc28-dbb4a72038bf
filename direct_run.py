#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
تشغيل مباشر للنظام الكامل
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """تشغيل النظام مباشرة"""
    try:
        print("🚀 بدء تشغيل النظام...")
        
        # استيراد البرنامج الرئيسي
        from main_tkinter import POSApp
        
        # بيانات مستخدم افتراضية
        user_info = {
            'username': 'admin',
            'full_name': 'المدير العام',
            'role': 'admin'
        }
        
        print("🏪 تشغيل البرنامج الرئيسي...")
        
        # إنشاء وتشغيل البرنامج الرئيسي مباشرة
        app = POSApp(user_info=user_info)
        
        # التأكد من ظهور النافذة
        app.root.deiconify()  # إظهار النافذة
        app.root.lift()       # رفع النافذة للمقدمة
        app.root.focus_force() # التركيز على النافذة
        app.root.attributes('-topmost', True)  # في المقدمة
        app.root.after(100, lambda: app.root.attributes('-topmost', False))
        
        print("✅ النظام جاهز!")
        app.run()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        # إنشاء نافذة خطأ بسيطة
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ", f"فشل في تشغيل النظام:\n{e}")
        root.destroy()

if __name__ == "__main__":
    main()
