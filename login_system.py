#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نظام تسجيل الدخول لنظام نقاط البيع
"""

import tkinter as tk
from tkinter import messagebox, font
import hashlib
import sqlite3
import os
from datetime import datetime


class LoginSystem:
    """نظام تسجيل الدخول"""
    
    def __init__(self):
        self.db_path = "users.db"
        self.setup_database()
        self.current_user = None
        
    def setup_database(self):
        """إعداد قاعدة بيانات المستخدمين"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # إنشاء جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT DEFAULT 'cashier',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # إضافة مستخدم افتراضي (المدير)
        admin_password = self.hash_password("admin123")
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, password_hash, full_name, role)
            VALUES (?, ?, ?, ?)
        ''', ("admin", admin_password, "المدير العام", "admin"))
        
        # إضافة كاشير افتراضي
        cashier_password = self.hash_password("cashier123")
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, password_hash, full_name, role)
            VALUES (?, ?, ?, ?)
        ''', ("cashier", cashier_password, "الكاشير", "cashier"))
        
        conn.commit()
        conn.close()
    
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_login(self, username, password):
        """التحقق من بيانات تسجيل الدخول"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        password_hash = self.hash_password(password)
        cursor.execute('''
            SELECT id, username, full_name, role FROM users 
            WHERE username = ? AND password_hash = ? AND is_active = 1
        ''', (username, password_hash))
        
        user = cursor.fetchone()
        
        if user:
            # تحديث وقت آخر تسجيل دخول
            cursor.execute('''
                UPDATE users SET last_login = CURRENT_TIMESTAMP 
                WHERE id = ?
            ''', (user[0],))
            conn.commit()
            
            self.current_user = {
                'id': user[0],
                'username': user[1],
                'full_name': user[2],
                'role': user[3]
            }
            conn.close()
            return self.current_user

        conn.close()
        return None


class LoginWindow:
    """نافذة تسجيل الدخول"""
    
    def __init__(self, on_success_callback=None):
        self.login_system = LoginSystem()
        self.on_success_callback = on_success_callback
        self.create_login_window()
        
    def create_login_window(self):
        """إنشاء نافذة تسجيل الدخول"""
        self.root = tk.Tk()
        self.root.title("🔐 تسجيل الدخول - نظام نقاط البيع")
        self.root.geometry("500x600")
        self.root.configure(bg='#f8f9fa')
        self.root.resizable(False, False)

        # التأكد من ظهور النافذة في المقدمة
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(lambda: self.root.attributes('-topmost', False))
        self.root.focus_force()

        # توسيط النافذة
        self.center_window()
        
        # الخطوط
        self.title_font = font.Font(family='Segoe UI', size=24, weight='bold')
        self.subtitle_font = font.Font(family='Segoe UI', size=14)
        self.label_font = font.Font(family='Segoe UI', size=12)
        self.button_font = font.Font(family='Segoe UI', size=12, weight='bold')
        
        self.create_widgets()
        
    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الحاوية الرئيسية
        main_frame = tk.Frame(self.root, bg='#ffffff', relief='raised', bd=3)
        main_frame.pack(fill='both', expand=True, padx=30, pady=30)
        
        # الهيدر
        header_frame = tk.Frame(main_frame, bg='#667eea', height=120)
        header_frame.pack(fill='x', padx=0, pady=0)
        header_frame.pack_propagate(False)
        
        # شعار وعنوان
        logo_label = tk.Label(header_frame, text="🏪", font=('Segoe UI', 48), 
                             bg='#667eea', fg='white')
        logo_label.pack(pady=(20, 5))
        
        title_label = tk.Label(header_frame, text="نظام نقاط البيع", 
                              font=self.title_font, bg='#667eea', fg='white')
        title_label.pack()
        
        subtitle_label = tk.Label(header_frame, text="محمد الاشرافى - مقهى وألعاب", 
                                 font=self.subtitle_font, bg='#667eea', fg='#e3f2fd')
        subtitle_label.pack(pady=(0, 15))
        
        # منطقة تسجيل الدخول
        login_frame = tk.Frame(main_frame, bg='#ffffff')
        login_frame.pack(fill='both', expand=True, padx=40, pady=40)
        
        # عنوان تسجيل الدخول
        login_title = tk.Label(login_frame, text="🔐 تسجيل الدخول", 
                              font=('Segoe UI', 18, 'bold'), bg='#ffffff', fg='#495057')
        login_title.pack(pady=(0, 30))
        
        # حقل اسم المستخدم
        username_label = tk.Label(login_frame, text="👤 اسم المستخدم:", 
                                 font=self.label_font, bg='#ffffff', fg='#495057')
        username_label.pack(anchor='w', pady=(0, 5))
        
        self.username_var = tk.StringVar()
        self.username_entry = tk.Entry(login_frame, textvariable=self.username_var,
                                      font=self.label_font, relief='solid', bd=2,
                                      bg='#f8f9fa', fg='#495057', justify='center')
        self.username_entry.pack(fill='x', pady=(0, 20), ipady=8)
        self.username_entry.focus()
        
        # حقل كلمة المرور
        password_label = tk.Label(login_frame, text="🔒 كلمة المرور:", 
                                 font=self.label_font, bg='#ffffff', fg='#495057')
        password_label.pack(anchor='w', pady=(0, 5))
        
        self.password_var = tk.StringVar()
        self.password_entry = tk.Entry(login_frame, textvariable=self.password_var,
                                      font=self.label_font, relief='solid', bd=2,
                                      bg='#f8f9fa', fg='#495057', show='*', justify='center')
        self.password_entry.pack(fill='x', pady=(0, 30), ipady=8)
        
        # زر تسجيل الدخول
        login_button = tk.Button(login_frame, text="🚀 دخول", font=self.button_font,
                                bg='#28a745', fg='white', relief='raised', bd=3,
                                activebackground='#218838', activeforeground='white',
                                cursor='hand2', pady=12, command=self.login)
        login_button.pack(fill='x', pady=(0, 15))
        
        # معلومات المستخدمين الافتراضيين
        info_frame = tk.Frame(login_frame, bg='#e9ecef', relief='raised', bd=2)
        info_frame.pack(fill='x', pady=20)
        
        info_title = tk.Label(info_frame, text="ℹ️ بيانات تسجيل الدخول الافتراضية:", 
                             font=('Segoe UI', 11, 'bold'), bg='#e9ecef', fg='#495057')
        info_title.pack(pady=(10, 5))
        
        admin_info = tk.Label(info_frame, text="👨‍💼 المدير: admin / admin123", 
                             font=('Segoe UI', 10), bg='#e9ecef', fg='#6c757d')
        admin_info.pack(pady=2)
        
        cashier_info = tk.Label(info_frame, text="👨‍💻 الكاشير: cashier / cashier123", 
                               font=('Segoe UI', 10), bg='#e9ecef', fg='#6c757d')
        cashier_info.pack(pady=(2, 10))
        
        # ربط Enter بتسجيل الدخول
        self.root.bind('<Return>', lambda event: self.login())
        
    def login(self):
        """تسجيل الدخول"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        user_info = self.login_system.verify_login(username, password)
        if user_info:
            messagebox.showinfo("نجح تسجيل الدخول",
                               f"مرحباً {user_info['full_name']}\nتم تسجيل الدخول بنجاح!")

            # إخفاء نافذة تسجيل الدخول
            self.root.withdraw()

            # تشغيل البرنامج الرئيسي
            if self.on_success_callback:
                self.on_success_callback(user_info)
            else:
                self.run_main_app(user_info)
        else:
            messagebox.showerror("خطأ في تسجيل الدخول", 
                               "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_var.set("")
            self.password_entry.focus()
    
    def run_main_app(self, user):
        """تشغيل البرنامج الرئيسي"""
        try:
            from main_tkinter import POSApp
            app = POSApp(user_info=user)
            app.run()
        except ImportError:
            messagebox.showerror("خطأ", "لا يمكن العثور على البرنامج الرئيسي")
        finally:
            self.root.quit()
    
    def run(self):
        """تشغيل نافذة تسجيل الدخول"""
        self.root.mainloop()


if __name__ == "__main__":
    login_window = LoginWindow()
    login_window.run()
