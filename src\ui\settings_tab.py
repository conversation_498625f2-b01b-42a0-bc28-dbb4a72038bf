#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
وحدة تبويب الإعدادات لنظام محمد الاشرافى
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
                            QTableWidget, QTableWidgetItem, QLineEdit, QSpinBox,
                            QFormLayout, QGroupBox, QMessageBox, QHeaderView,
                            QTabWidget, QComboBox, QDoubleSpinBox, QDialog, QDialogButtonBox)
from PyQt5.QtCore import Qt, QRegExp
from PyQt5.QtGui import QRegExpValidator

class ProductDialog(QDialog):
    """Dialog for adding or editing products"""

    def __init__(self, parent=None, product=None):
        """Initialize the dialog

        Args:
            parent: The parent widget
            product: Product dictionary if editing, None if adding new
        """
        super().__init__(parent)

        self.product = product or {}
        self.init_ui()

    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("إضافة/تحرير منتج")
        self.setMinimumWidth(400)

        layout = QVBoxLayout(self)

        # Form layout
        form = QFormLayout()

        self.name_edit = QLineEdit(self.product.get("name", ""))
        form.addRow("اسم المنتج:", self.name_edit)

        self.price_spin = QDoubleSpinBox()
        self.price_spin.setRange(0, 1000)
        self.price_spin.setValue(float(self.product.get("price", 0)))
        form.addRow("السعر:", self.price_spin)

        self.category_combo = QComboBox()
        self.category_combo.addItems(["مشروبات", "مأكولات", "سناكس", "أخرى"])
        if "category" in self.product:
            self.category_combo.setCurrentText(self.product["category"])
        form.addRow("التصنيف:", self.category_combo)

        self.cost_spin = QDoubleSpinBox()
        self.cost_spin.setRange(0, 1000)
        self.cost_spin.setValue(float(self.product.get("cost", 0)))
        form.addRow("التكلفة:", self.cost_spin)

        self.stock_spin = QSpinBox()
        self.stock_spin.setRange(0, 1000)
        self.stock_spin.setValue(int(self.product.get("stock", 0)))
        form.addRow("المخزون:", self.stock_spin)

        layout.addLayout(form)

        # Buttons
        btn_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        btn_box.accepted.connect(self.accept)
        btn_box.rejected.connect(self.reject)
        layout.addWidget(btn_box)

    def get_product(self):
        """Get the product data"""
        return {
            "id": self.product.get("id"),
            "name": self.name_edit.text(),
            "price": self.price_spin.value(),
            "category": self.category_combo.currentText(),
            "cost": self.cost_spin.value(),
            "stock": self.stock_spin.value()
        }

class StationDialog(QDialog):
    """Dialog for adding or editing PlayStation stations"""

    def __init__(self, parent=None, station=None):
        """Initialize the dialog

        Args:
            parent: The parent widget
            station: Station dictionary if editing, None if adding new
        """
        super().__init__(parent)

        self.station = station or {}
        self.init_ui()

    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("إضافة/تحرير محطة بلايستيشن")
        self.setMinimumWidth(400)

        layout = QVBoxLayout(self)

        # Form layout
        form = QFormLayout()

        self.name_edit = QLineEdit(self.station.get("name", ""))
        form.addRow("اسم المحطة:", self.name_edit)

        self.type_combo = QComboBox()
        self.type_combo.addItems(["PS5", "PS4", "PS3", "أخرى"])
        if "type" in self.station:
            self.type_combo.setCurrentText(self.station["type"])
        form.addRow("النوع:", self.type_combo)

        self.rate_spin = QDoubleSpinBox()
        self.rate_spin.setRange(0, 100)
        self.rate_spin.setValue(float(self.station.get("rate", 20)))
        form.addRow("السعر/ساعة:", self.rate_spin)

        layout.addLayout(form)

        # Buttons
        btn_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        btn_box.accepted.connect(self.accept)
        btn_box.rejected.connect(self.reject)
        layout.addWidget(btn_box)

    def get_station(self):
        """Get the station data"""
        return {
            "id": self.station.get("id"),
            "name": self.name_edit.text(),
            "type": self.type_combo.currentText(),
            "rate": self.rate_spin.value(),
            "status": self.station.get("status", "available")
        }

class UserDialog(QDialog):
    """Dialog for adding or editing users"""

    def __init__(self, parent=None, user=None):
        """Initialize the dialog

        Args:
            parent: The parent widget
            user: User dictionary if editing, None if adding new
        """
        super().__init__(parent)

        self.user = user or {}
        self.init_ui()

    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("إضافة/تحرير مستخدم")
        self.setMinimumWidth(400)

        layout = QVBoxLayout(self)

        # Form layout
        form = QFormLayout()

        self.username_edit = QLineEdit(self.user.get("username", ""))
        form.addRow("اسم المستخدم:", self.username_edit)

        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        form.addRow("كلمة المرور:", self.password_edit)

        self.fullname_edit = QLineEdit(self.user.get("full_name", ""))
        form.addRow("الاسم الكامل:", self.fullname_edit)

        self.role_combo = QComboBox()
        self.role_combo.addItems(["مدير", "موظف", "محاسب"])
        if "role" in self.user:
            self.role_combo.setCurrentText(self.user["role"])
        form.addRow("الدور:", self.role_combo)

        layout.addLayout(form)

        # Buttons
        btn_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        btn_box.accepted.connect(self.accept)
        btn_box.rejected.connect(self.reject)
        layout.addWidget(btn_box)

    def get_user(self):
        """Get the user data"""
        user_data = {
            "id": self.user.get("id"),
            "username": self.username_edit.text(),
            "full_name": self.fullname_edit.text(),
            "role": self.role_combo.currentText()
        }

        if self.password_edit.text():
            user_data["password"] = self.password_edit.text()

        return user_data

class SettingsTab(QWidget):
    """Settings tab for the application"""

    def __init__(self, db_manager, config):
        """Initialize the settings tab"""
        super().__init__()

        self.db_manager = db_manager
        self.config = config
        self.products = []
        self.stations = []
        self.users = []

        # Initialize UI
        self.init_ui()

        # Load initial data
        self.load_data()

    def init_ui(self):
        """Initialize the user interface"""
        main_layout = QVBoxLayout(self)

        # Header
        header_layout = QHBoxLayout()
        title_label = QLabel("الإعدادات")
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold;")
        header_layout.addWidget(title_label)
        main_layout.addLayout(header_layout)

        # Create tab widget for different settings
        self.settings_tabs = QTabWidget()

        # General settings tab
        self.general_tab = QWidget()
        general_layout = QFormLayout(self.general_tab)

        self.store_name_edit = QLineEdit(self.config.get("app_name", "محمد الاشرافى"))
        general_layout.addRow("اسم المتجر:", self.store_name_edit)

        self.tax_rate_spin = QDoubleSpinBox()
        self.tax_rate_spin.setRange(0, 100)
        self.tax_rate_spin.setValue(float(self.config.get("tax_rate", 15)))
        self.tax_rate_spin.setSuffix("%")
        general_layout.addRow("نسبة الضريبة:", self.tax_rate_spin)

        self.currency_edit = QLineEdit(self.config.get("currency_symbol", "ريال"))
        general_layout.addRow("رمز العملة:", self.currency_edit)

        self.ps_rate_spin = QDoubleSpinBox()
        self.ps_rate_spin.setRange(0, 100)
        self.ps_rate_spin.setValue(float(self.config.get("default_ps_rate", 20)))
        general_layout.addRow("سعر البلايستيشن الافتراضي/ساعة:", self.ps_rate_spin)

        self.save_general_btn = QPushButton("حفظ الإعدادات العامة")
        self.save_general_btn.clicked.connect(self.save_general_settings)
        general_layout.addRow("", self.save_general_btn)

        self.settings_tabs.addTab(self.general_tab, "إعدادات عامة")

        # Products tab
        self.products_tab = QWidget()
        products_layout = QVBoxLayout(self.products_tab)

        products_header = QHBoxLayout()
        products_header.addWidget(QLabel("إدارة المنتجات"))

        self.add_product_btn = QPushButton("إضافة منتج")
        self.add_product_btn.clicked.connect(self.add_product)
        products_header.addWidget(self.add_product_btn)

        self.edit_product_btn = QPushButton("تحرير منتج")
        self.edit_product_btn.clicked.connect(self.edit_product)
        products_header.addWidget(self.edit_product_btn)

        self.delete_product_btn = QPushButton("حذف منتج")
        self.delete_product_btn.clicked.connect(self.delete_product)
        products_header.addWidget(self.delete_product_btn)

        products_layout.addLayout(products_header)

        self.products_table = QTableWidget()
        self.products_table.setColumnCount(6)
        self.products_table.setHorizontalHeaderLabels(["ID", "اسم المنتج", "السعر", "التصنيف", "التكلفة", "المخزون"])
        self.products_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.products_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.products_table.setSelectionMode(QTableWidget.SingleSelection)
        products_layout.addWidget(self.products_table)

        self.settings_tabs.addTab(self.products_tab, "المنتجات")

        # PlayStation stations tab
        self.stations_tab = QWidget()
        stations_layout = QVBoxLayout(self.stations_tab)

        stations_header = QHBoxLayout()
        stations_header.addWidget(QLabel("إدارة محطات البلايستيشن"))

        self.add_station_btn = QPushButton("إضافة محطة")
        self.add_station_btn.clicked.connect(self.add_station)
        stations_header.addWidget(self.add_station_btn)

        self.edit_station_btn = QPushButton("تحرير محطة")
        self.edit_station_btn.clicked.connect(self.edit_station)
        stations_header.addWidget(self.edit_station_btn)

        self.delete_station_btn = QPushButton("حذف محطة")
        self.delete_station_btn.clicked.connect(self.delete_station)
        stations_header.addWidget(self.delete_station_btn)

        stations_layout.addLayout(stations_header)

        self.stations_table = QTableWidget()
        self.stations_table.setColumnCount(5)
        self.stations_table.setHorizontalHeaderLabels(["ID", "اسم المحطة", "النوع", "السعر/ساعة", "الحالة"])
        self.stations_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.stations_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.stations_table.setSelectionMode(QTableWidget.SingleSelection)
        stations_layout.addWidget(self.stations_table)

        self.settings_tabs.addTab(self.stations_tab, "محطات البلايستيشن")

        # Users tab
        self.users_tab = QWidget()
        users_layout = QVBoxLayout(self.users_tab)

        users_header = QHBoxLayout()
        users_header.addWidget(QLabel("إدارة المستخدمين"))

        self.add_user_btn = QPushButton("إضافة مستخدم")
        self.add_user_btn.clicked.connect(self.add_user)
        users_header.addWidget(self.add_user_btn)

        self.edit_user_btn = QPushButton("تحرير مستخدم")
        self.edit_user_btn.clicked.connect(self.edit_user)
        users_header.addWidget(self.edit_user_btn)

        self.delete_user_btn = QPushButton("حذف مستخدم")
        self.delete_user_btn.clicked.connect(self.delete_user)
        users_header.addWidget(self.delete_user_btn)

        users_layout.addLayout(users_header)

        self.users_table = QTableWidget()
        self.users_table.setColumnCount(4)
        self.users_table.setHorizontalHeaderLabels(["ID", "اسم المستخدم", "الاسم الكامل", "الدور"])
        self.users_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.users_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.users_table.setSelectionMode(QTableWidget.SingleSelection)
        users_layout.addWidget(self.users_table)

        self.settings_tabs.addTab(self.users_tab, "المستخدمين")

        main_layout.addWidget(self.settings_tabs)

    def load_data(self):
        """Load all settings data"""
        try:
            self.load_products()
            self.load_stations()
            self.load_users()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")

    def load_products(self):
        """Load products from database"""
        # In a real implementation, this would load from database
        # For now, create some sample products
        self.products = [
            {"id": 1, "name": "قهوة عربية", "price": 15, "category": "مشروبات", "cost": 5, "stock": 100},
            {"id": 2, "name": "قهوة أمريكية", "price": 18, "category": "مشروبات", "cost": 6, "stock": 80},
            {"id": 3, "name": "كابتشينو", "price": 20, "category": "مشروبات", "cost": 7, "stock": 70},
            {"id": 4, "name": "شاي", "price": 10, "category": "مشروبات", "cost": 3, "stock": 120},
            {"id": 5, "name": "عصير برتقال", "price": 15, "category": "مشروبات", "cost": 5, "stock": 50},
            {"id": 6, "name": "سندويش جبن", "price": 22, "category": "مأكولات", "cost": 10, "stock": 30},
            {"id": 7, "name": "كيك شوكولاتة", "price": 25, "category": "سناكس", "cost": 12, "stock": 25},
        ]
        self.update_products_table()

    def load_stations(self):
        """Load PlayStation stations from database"""
        # In a real implementation, this would load from database
        # For now, create some sample stations
        self.stations = [
            {"id": 1, "name": "PS5-1", "type": "PS5", "rate": 25, "status": "available"},
            {"id": 2, "name": "PS5-2", "type": "PS5", "rate": 25, "status": "available"},
            {"id": 3, "name": "PS4-1", "type": "PS4", "rate": 20, "status": "available"},
            {"id": 4, "name": "PS4-2", "type": "PS4", "rate": 20, "status": "available"},
        ]
        self.update_stations_table()

    def load_users(self):
        """Load users from database"""
        # In a real implementation, this would load from database
        # For now, create some sample users
        self.users = [
            {"id": 1, "username": "admin", "full_name": "مدير النظام", "role": "مدير"},
            {"id": 2, "username": "cashier", "full_name": "موظف الكاشير", "role": "موظف"},
            {"id": 3, "username": "accountant", "full_name": "المحاسب", "role": "محاسب"},
        ]
        self.update_users_table()

    def update_products_table(self):
        """Update the products table"""
        self.products_table.setRowCount(len(self.products))
        for i, product in enumerate(self.products):
            self.products_table.setItem(i, 0, QTableWidgetItem(str(product["id"])))
            self.products_table.setItem(i, 1, QTableWidgetItem(product["name"]))
            self.products_table.setItem(i, 2, QTableWidgetItem(str(product["price"])))
            self.products_table.setItem(i, 3, QTableWidgetItem(product["category"]))
            self.products_table.setItem(i, 4, QTableWidgetItem(str(product["cost"])))
            self.products_table.setItem(i, 5, QTableWidgetItem(str(product["stock"])))

    def update_stations_table(self):
        """Update the stations table"""
        self.stations_table.setRowCount(len(self.stations))
        for i, station in enumerate(self.stations):
            self.stations_table.setItem(i, 0, QTableWidgetItem(str(station["id"])))
            self.stations_table.setItem(i, 1, QTableWidgetItem(station["name"]))
            self.stations_table.setItem(i, 2, QTableWidgetItem(station["type"]))
            self.stations_table.setItem(i, 3, QTableWidgetItem(str(station["rate"])))
            self.stations_table.setItem(i, 4, QTableWidgetItem(station["status"]))

    def update_users_table(self):
        """Update the users table"""
        self.users_table.setRowCount(len(self.users))
        for i, user in enumerate(self.users):
            self.users_table.setItem(i, 0, QTableWidgetItem(str(user["id"])))
            self.users_table.setItem(i, 1, QTableWidgetItem(user["username"]))
            self.users_table.setItem(i, 2, QTableWidgetItem(user["full_name"]))
            self.users_table.setItem(i, 3, QTableWidgetItem(user["role"]))

    def save_general_settings(self):
        """Save general settings"""
        try:
            # In a real implementation, this would save to database/config file
            self.config["app_name"] = self.store_name_edit.text()
            self.config["tax_rate"] = self.tax_rate_spin.value()
            self.config["currency_symbol"] = self.currency_edit.text()
            self.config["default_ps_rate"] = self.ps_rate_spin.value()

            QMessageBox.information(self, "نجاح", "تم حفظ الإعدادات بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الإعدادات: {str(e)}")

    def add_product(self):
        """Add a new product"""
        dialog = ProductDialog(self)
        if dialog.exec_():
            product = dialog.get_product()

            # In a real implementation, this would save to database
            # For now, just add to the list
            product["id"] = max([p["id"] for p in self.products]) + 1 if self.products else 1
            self.products.append(product)
            self.update_products_table()

    def edit_product(self):
        """Edit the selected product"""
        selected_rows = self.products_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد منتج للتحرير")
            return

        row = selected_rows[0].row()
        product_id = int(self.products_table.item(row, 0).text())

        # Find the product
        product = next((p for p in self.products if p["id"] == product_id), None)
        if not product:
            QMessageBox.warning(self, "تحذير", "لم يتم العثور على المنتج")
            return

        dialog = ProductDialog(self, product)
        if dialog.exec_():
            updated_product = dialog.get_product()

            # In a real implementation, this would update the database
            # For now, just update the list
            for i, p in enumerate(self.products):
                if p["id"] == product_id:
                    self.products[i] = updated_product
                    break

            self.update_products_table()

    def delete_product(self):
        """Delete the selected product"""
        selected_rows = self.products_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد منتج للحذف")
            return

        row = selected_rows[0].row()
        product_id = int(self.products_table.item(row, 0).text())

        reply = QMessageBox.question(self, "تأكيد الحذف", "هل أنت متأكد من حذف هذا المنتج؟",
                                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            # In a real implementation, this would delete from database
            # For now, just remove from the list
            self.products = [p for p in self.products if p["id"] != product_id]
            self.update_products_table()

    def add_station(self):
        """Add a new PlayStation station"""
        dialog = StationDialog(self)
        if dialog.exec_():
            station = dialog.get_station()

            # In a real implementation, this would save to database
            # For now, just add to the list
            station["id"] = max([s["id"] for s in self.stations]) + 1 if self.stations else 1
            self.stations.append(station)
            self.update_stations_table()

    def edit_station(self):
        """Edit the selected station"""
        selected_rows = self.stations_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد محطة للتحرير")
            return

        row = selected_rows[0].row()
        station_id = int(self.stations_table.item(row, 0).text())

        # Find the station
        station = next((s for s in self.stations if s["id"] == station_id), None)
        if not station:
            QMessageBox.warning(self, "تحذير", "لم يتم العثور على المحطة")
            return

        dialog = StationDialog(self, station)
        if dialog.exec_():
            updated_station = dialog.get_station()

            # In a real implementation, this would update the database
            # For now, just update the list
            for i, s in enumerate(self.stations):
                if s["id"] == station_id:
                    self.stations[i] = updated_station
                    break

            self.update_stations_table()

    def delete_station(self):
        """Delete the selected station"""
        selected_rows = self.stations_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد محطة للحذف")
            return

        row = selected_rows[0].row()
        station_id = int(self.stations_table.item(row, 0).text())

        reply = QMessageBox.question(self, "تأكيد الحذف", "هل أنت متأكد من حذف هذه المحطة؟",
                                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            # In a real implementation, this would delete from database
            # For now, just remove from the list
            self.stations = [s for s in self.stations if s["id"] != station_id]
            self.update_stations_table()

    def add_user(self):
        """Add a new user"""
        dialog = UserDialog(self)
        if dialog.exec_():
            user = dialog.get_user()

            # In a real implementation, this would save to database
            # For now, just add to the list
            user["id"] = max([u["id"] for u in self.users]) + 1 if self.users else 1
            self.users.append(user)
            self.update_users_table()

    def edit_user(self):
        """Edit the selected user"""
        selected_rows = self.users_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد مستخدم للتحرير")
            return

        row = selected_rows[0].row()
        user_id = int(self.users_table.item(row, 0).text())

        # Find the user
        user = next((u for u in self.users if u["id"] == user_id), None)
        if not user:
            QMessageBox.warning(self, "تحذير", "لم يتم العثور على المستخدم")
            return

        dialog = UserDialog(self, user)
        if dialog.exec_():
            updated_user = dialog.get_user()

            # In a real implementation, this would update the database
            # For now, just update the list
            for i, u in enumerate(self.users):
                if u["id"] == user_id:
                    self.users[i] = updated_user
                    break

            self.update_users_table()

    def delete_user(self):
        """Delete the selected user"""
        selected_rows = self.users_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد مستخدم للحذف")
            return

        row = selected_rows[0].row()
        user_id = int(self.users_table.item(row, 0).text())

        # Don't allow deleting the admin user
        if user_id == 1:
            QMessageBox.warning(self, "تحذير", "لا يمكن حذف المستخدم المسؤول")
            return

        reply = QMessageBox.question(self, "تأكيد الحذف", "هل أنت متأكد من حذف هذا المستخدم؟",
                                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            # In a real implementation, this would delete from database
            # For now, just remove from the list
            self.users = [u for u in self.users if u["id"] != user_id]
            self.update_users_table()
