#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Configuration utilities for محمد الاشرافى POS System
"""

import os
import json
from pathlib import Path

DEFAULT_CONFIG = {
    "app_name": "محمد الاشرافى",
    "version": "1.0.0",
    "database": "pos_data.db",
    "theme": "dark",
    "language": "ar",
    "receipt_printer": "",
    "currency_symbol": "ريال",
    "tax_rate": 0.0,
    "receipt_header": "محمد الاشرافى",
    "receipt_footer": "شكرا لزيارتكم",
    "playstation_rates": {
        "ps4": 10.0,
        "ps5": 15.0
    }
}

def initialize_config():
    """Initialize the application configuration"""
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    config_path = os.path.join(base_dir, "data")
    config_file = os.path.join(config_path, "config.json")

    # Create data directory if it doesn't exist
    Path(config_path).mkdir(parents=True, exist_ok=True)

    # Create config file if it doesn't exist
    if not os.path.exists(config_file):
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(DEFAULT_CONFIG, f, ensure_ascii=False, indent=4)

    # Load configuration
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception:
        # If there's an error loading the config, use default
        config = DEFAULT_CONFIG

    return config

def save_config(config):
    """Save the configuration to file"""
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    config_path = os.path.join(base_dir, "data")
    config_file = os.path.join(config_path, "config.json")

    # Create data directory if it doesn't exist
    Path(config_path).mkdir(parents=True, exist_ok=True)

    # Save configuration
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        print(f"Error saving config: {e}")
        return False
