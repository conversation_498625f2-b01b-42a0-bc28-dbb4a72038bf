# 🏪 نظام نقاط البيع - محمد الاشرافى

نظام نقاط بيع متكامل لإدارة مقهى ومركز ألعاب البلايستيشن مع واجهة مستخدم جميلة ومميزات متقدمة.

## ✨ المميزات الرئيسية

### 🔐 نظام تسجيل الدخول
- واجهة تسجيل دخول أنيقة ومؤمنة
- إدارة المستخدمين مع أدوار مختلفة (مدير، كاشير)
- تشفير كلمات المرور
- تتبع آخر تسجيل دخول

### ☕ إدارة المقهى
- عرض المنتجات بتصميم جميل ومنظم
- تصنيف المنتجات حسب الفئات
- إضافة المنتجات للطلب بسهولة
- حساب المجموع تلقائياً
- تطبيق الخصومات

### 🎮 إدارة البلايستيشن
- إدارة محطات البلايستيشن المتعددة
- مؤقتات في الوقت الفعلي
- تتبع الأرباح لكل محطة
- واجهة تحكم متقدمة لكل محطة
- إدارة جلسات العملاء

### 🍽️ نظام الطاولات المتعددة
- إدارة طاولات متعددة بشكل منفصل
- التبديل السهل بين الطاولات
- حفظ الطلبات لكل طاولة
- عرض حالة الطاولات (نشطة/فارغة)

### 💱 نظام العملات المتعدد
- دعم عملات متعددة (ريال سعودي، دولار، يورو، درهم)
- تحويل الأسعار تلقائياً
- عرض الأسعار بالعملة المختارة

### 🧾 نظام طباعة الفواتير
- إنشاء فواتير احترافية بتنسيق HTML
- طباعة الفواتير أو حفظها
- تفاصيل كاملة للطلب والأسعار
- معلومات المتجر والتاريخ

### 📦 إدارة المخزون
- عرض جميع المنتجات
- إضافة منتجات جديدة
- تعديل الأسعار والمعلومات
- إدارة الفئات

### 📊 التقارير
- تقارير المبيعات اليومية
- إحصائيات الأرباح
- تقارير استخدام البلايستيشن

## 🚀 كيفية التشغيل

### المتطلبات
- Python 3.7 أو أحدث
- مكتبات Python المطلوبة (مثبتة تلقائياً):
  - tkinter (مدمجة مع Python)
  - sqlite3 (مدمجة مع Python)
  - datetime (مدمجة مع Python)

### التشغيل
1. تأكد من وجود جميع الملفات في نفس المجلد
2. شغل الأمر التالي:
```bash
python run_pos.py
```

### بيانات تسجيل الدخول الافتراضية
- **المدير العام:**
  - اسم المستخدم: `admin`
  - كلمة المرور: `admin123`

- **الكاشير:**
  - اسم المستخدم: `cashier`
  - كلمة المرور: `cashier123`

## 📁 هيكل الملفات

```
├── run_pos.py              # ملف التشغيل الرئيسي
├── main_tkinter.py         # التطبيق الرئيسي
├── login_system.py         # نظام تسجيل الدخول
├── pos_database.db         # قاعدة بيانات المنتجات
├── users.db               # قاعدة بيانات المستخدمين
└── README.md              # هذا الملف
```

## 🎨 التصميم والواجهة

- تصميم عصري وجميل باستخدام Tkinter
- دعم كامل للغة العربية مع تخطيط RTL
- ألوان متناسقة ومريحة للعين
- أيقونات تعبيرية لسهولة الاستخدام
- تجربة مستخدم سلسة ومتجاوبة

## 🔧 الإعدادات والتخصيص

### إضافة منتجات جديدة
1. انتقل لتبويب "المخزون"
2. اضغط على "إضافة منتج جديد"
3. أدخل تفاصيل المنتج
4. اختر الفئة المناسبة

### إدارة المستخدمين
- يمكن للمدير إضافة مستخدمين جدد من خلال قاعدة البيانات
- تعديل الأدوار والصلاحيات

### تخصيص العملات
- يمكن تعديل أسعار الصرف في الكود
- إضافة عملات جديدة حسب الحاجة

## 🛠️ الدعم الفني

للمساعدة أو الإبلاغ عن مشاكل:
- تأكد من تشغيل Python بشكل صحيح
- تحقق من وجود جميع الملفات
- راجع رسائل الخطأ في وحدة التحكم

## 📝 ملاحظات مهمة

- يتم حفظ البيانات تلقائياً في قواعد البيانات
- النظام يدعم النسخ الاحتياطي لقواعد البيانات
- يُنصح بعمل نسخة احتياطية دورية من ملفات قواعد البيانات

## 🔄 التحديثات المستقبلية

- إضافة تقارير أكثر تفصيلاً
- دعم الباركود
- تكامل مع أنظمة الدفع الإلكتروني
- تطبيق ويب للإدارة عن بُعد

---

**تم تطوير هذا النظام بعناية فائقة لضمان أفضل تجربة استخدام ممكنة** 🌟
