# محمد الاشرافى - Café & PlayStation POS System

A complete Point of Sale (POS) system for managing a café and PlayStation gaming center.

## Features

- Café POS with menu management and order processing
- PlayStation time tracking and billing
- Customer management
- Inventory tracking
- Sales reporting and analytics
- User authentication

## Requirements

- Python 3.8+
- PyQt5
- SQLite3

## Installation

1. Install required packages:
   ```
   pip install PyQt5 pillow qrcode
   ```

2. Run the application:
   ```
   python main.py
   ```
