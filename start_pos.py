#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف تشغيل مبسط لنظام نقاط البيع
"""

import sys
import os

def main():
    """تشغيل النظام مباشرة"""
    try:
        print("🚀 بدء تشغيل نظام نقاط البيع...")
        print("📁 التحقق من الملفات المطلوبة...")

        # إضافة المجلد الحالي إلى مسار Python
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

        # التحقق من وجود الملفات المطلوبة
        required_files = ['login_system.py', 'main_tkinter.py']
        for file in required_files:
            if not os.path.exists(file):
                raise FileNotFoundError(f"الملف المطلوب غير موجود: {file}")

        # استيراد الملفات المطلوبة
        print("📋 تحميل نظام تسجيل الدخول...")
        from login_system import LoginWindow
        from main_tkinter import POSApp

        def on_login_success(user_info):
            """تشغيل البرنامج الرئيسي بعد تسجيل الدخول"""
            print(f"✅ مرحباً {user_info['full_name']}")
            print("🏪 تشغيل البرنامج الرئيسي...")

            # إنشاء وتشغيل البرنامج الرئيسي
            app = POSApp(user_info=user_info)
            app.run()

        # إنشاء وتشغيل نافذة تسجيل الدخول
        print("� فتح نافذة تسجيل الدخول...")
        login_window = LoginWindow(on_success_callback=on_login_success)
        login_window.run()

        print("👋 تم إغلاق النظام")

    except ImportError as e:
        print(f"❌ خطأ في استيراد الملفات: {e}")
        input("اضغط Enter للخروج...")

    except FileNotFoundError as e:
        print(f"❌ {e}")
        print("تأكد من وجود جميع الملفات في نفس المجلد")
        input("اضغط Enter للخروج...")

    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
