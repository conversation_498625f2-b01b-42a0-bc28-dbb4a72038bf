#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف تشغيل مبسط لنظام نقاط البيع
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def main():
    """تشغيل النظام مباشرة"""
    try:
        print("🚀 بدء تشغيل نظام نقاط البيع...")
        
        # إضافة المجلد الحالي إلى مسار Python
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        # استيراد الملفات المطلوبة
        from login_system import LoginWindow
        from main_tkinter import POSApp
        
        def on_login_success(user_info):
            """تشغيل البرنامج الرئيسي بعد تسجيل الدخول"""
            print(f"✅ مرحباً {user_info['full_name']}")
            
            # إنشاء وتشغيل البرنامج الرئيسي
            app = POSApp(user_info=user_info)
            app.run()
        
        # إنشاء وتشغيل نافذة تسجيل الدخول
        print("📋 فتح نافذة تسجيل الدخول...")
        login_window = LoginWindow(on_success_callback=on_login_success)
        login_window.run()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الملفات: {e}")
        messagebox.showerror("خطأ", f"خطأ في استيراد الملفات:\n{e}")
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        messagebox.showerror("خطأ", f"خطأ غير متوقع:\n{e}")

if __name__ == "__main__":
    main()
