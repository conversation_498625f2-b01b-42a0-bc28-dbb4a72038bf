#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
PlayStation models for محمد الاشرافى POS System
"""

import datetime

class PlayStationStation:
    """PlayStation station model class"""

    def __init__(self, id=None, station_number=0, station_type="PS4", hourly_rate=0,
                 is_available=True, status="idle", created_at=None):
        self.id = id
        self.station_number = station_number
        self.station_type = station_type
        self.hourly_rate = hourly_rate
        self.is_available = is_available
        self.status = status
        self.created_at = created_at

    @staticmethod
    def from_dict(data):
        """Create a station instance from a dictionary"""
        return PlayStationStation(
            id=data.get('id'),
            station_number=data.get('station_number', 0),
            station_type=data.get('station_type', 'PS4'),
            hourly_rate=data.get('hourly_rate', 0),
            is_available=bool(data.get('is_available', True)),
            status=data.get('status', 'idle'),
            created_at=data.get('created_at')
        )

    def to_dict(self):
        """Convert station to dictionary"""
        return {
            'id': self.id,
            'station_number': self.station_number,
            'station_type': self.station_type,
            'hourly_rate': self.hourly_rate,
            'is_available': self.is_available,
            'status': self.status,
            'created_at': self.created_at
        }


class PlayStationSession:
    """PlayStation session model class"""

    def __init__(self, id=None, station_id=None, customer_name="", start_time=None,
                 end_time=None, duration=0, amount=0, status="active", notes=""):
        self.id = id
        self.station_id = station_id
        self.customer_name = customer_name
        self.start_time = start_time or datetime.datetime.now()
        self.end_time = end_time
        self.duration = duration  # In minutes
        self.amount = amount
        self.status = status
        self.notes = notes

        # Additional fields from joins
        self.station_number = 0
        self.station_type = ""
        self.hourly_rate = 0

    @staticmethod
    def from_dict(data):
        """Create a session instance from a dictionary"""
        session = PlayStationSession(
            id=data.get('id'),
            station_id=data.get('station_id'),
            customer_name=data.get('customer_name', ''),
            start_time=data.get('start_time'),
            end_time=data.get('end_time'),
            duration=data.get('duration', 0),
            amount=data.get('amount', 0),
            status=data.get('status', 'active'),
            notes=data.get('notes', '')
        )

        # Optional fields that may come from joins
        if 'station_number' in data:
            session.station_number = data.get('station_number', 0)

        if 'station_type' in data:
            session.station_type = data.get('station_type', '')

        if 'hourly_rate' in data:
            session.hourly_rate = data.get('hourly_rate', 0)

        return session

    def to_dict(self):
        """Convert session to dictionary"""
        return {
            'id': self.id,
            'station_id': self.station_id,
            'customer_name': self.customer_name,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'duration': self.duration,
            'amount': self.amount,
            'status': self.status,
            'notes': self.notes,
            'station_number': self.station_number,
            'station_type': self.station_type,
            'hourly_rate': self.hourly_rate
        }

    def calculate_duration_and_amount(self):
        """Calculate session duration and amount if end_time is set"""
        if not self.end_time:
            return

        # Calculate duration in minutes
        delta = self.end_time - self.start_time
        self.duration = int(delta.total_seconds() / 60)

        # Calculate amount
        hours = self.duration / 60
        self.amount = round(hours * self.hourly_rate, 2)
