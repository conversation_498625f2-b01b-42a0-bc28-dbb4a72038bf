#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Order models for محمد الاشرافى POS System
"""

import datetime

class Order:
    """Order model class"""

    def __init__(self, id=None, order_number="", customer_name="", order_type="cafe",
                 total_amount=0, discount_amount=0, tax_amount=0, final_amount=0,
                 payment_method="cash", order_status="completed", created_by=None, created_at=None):
        self.id = id
        self.order_number = order_number
        self.customer_name = customer_name
        self.order_type = order_type  # cafe or playstation
        self.total_amount = total_amount
        self.discount_amount = discount_amount
        self.tax_amount = tax_amount
        self.final_amount = final_amount
        self.payment_method = payment_method
        self.order_status = order_status
        self.created_by = created_by
        self.created_at = created_at or datetime.datetime.now()
        self.items = []

    @staticmethod
    def from_dict(data, items=None):
        """Create an order instance from a dictionary"""
        order = Order(
            id=data.get('id'),
            order_number=data.get('order_number', ''),
            customer_name=data.get('customer_name', ''),
            order_type=data.get('order_type', 'cafe'),
            total_amount=data.get('total_amount', 0),
            discount_amount=data.get('discount_amount', 0),
            tax_amount=data.get('tax_amount', 0),
            final_amount=data.get('final_amount', 0),
            payment_method=data.get('payment_method', 'cash'),
            order_status=data.get('order_status', 'completed'),
            created_by=data.get('created_by'),
            created_at=data.get('created_at')
        )

        if items:
            order.items = items
        elif 'items' in data:
            order.items = [OrderItem.from_dict(item) for item in data['items']]

        return order

    def to_dict(self):
        """Convert order to dictionary"""
        return {
            'id': self.id,
            'order_number': self.order_number,
            'customer_name': self.customer_name,
            'order_type': self.order_type,
            'total_amount': self.total_amount,
            'discount_amount': self.discount_amount,
            'tax_amount': self.tax_amount,
            'final_amount': self.final_amount,
            'payment_method': self.payment_method,
            'order_status': self.order_status,
            'created_by': self.created_by,
            'created_at': self.created_at,
            'items': [item.to_dict() for item in self.items]
        }

    def calculate_totals(self, tax_rate=0):
        """Calculate order totals"""
        # Calculate total amount
        self.total_amount = sum(item.total_price for item in self.items)

        # Calculate tax amount
        self.tax_amount = round(self.total_amount * tax_rate / 100, 2)

        # Calculate final amount
        self.final_amount = self.total_amount + self.tax_amount - self.discount_amount

        return self.final_amount


class OrderItem:
    """Order item model class"""

    def __init__(self, id=None, order_id=None, product_id=None, quantity=1,
                 unit_price=0, total_price=0, notes=""):
        self.id = id
        self.order_id = order_id
        self.product_id = product_id
        self.quantity = quantity
        self.unit_price = unit_price
        self.total_price = total_price or (quantity * unit_price)
        self.notes = notes

        # Additional fields from joins
        self.product_name = ""

    @staticmethod
    def from_dict(data):
        """Create an order item instance from a dictionary"""
        item = OrderItem(
            id=data.get('id'),
            order_id=data.get('order_id'),
            product_id=data.get('product_id'),
            quantity=data.get('quantity', 1),
            unit_price=data.get('unit_price', 0),
            total_price=data.get('total_price', 0),
            notes=data.get('notes', '')
        )

        # Optional fields that may come from joins
        if 'product_name' in data:
            item.product_name = data.get('product_name', '')

        return item

    def to_dict(self):
        """Convert order item to dictionary"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'product_id': self.product_id,
            'quantity': self.quantity,
            'unit_price': self.unit_price,
            'total_price': self.total_price,
            'notes': self.notes,
            'product_name': self.product_name
        }

    def calculate_total_price(self):
        """Calculate total price based on quantity and unit price"""
        self.total_price = round(self.quantity * self.unit_price, 2)
        return self.total_price
