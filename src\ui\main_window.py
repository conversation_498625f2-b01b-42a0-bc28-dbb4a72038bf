#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Main window for محمد الاشرافى POS System
"""

import sys
import os
from PyQt5.QtWidgets import (QMainWindow, QWidget, QTabWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QStatusBar, QMessageBox, QAction, 
                             QToolBar, QDialog, QInputDialog, QLineEdit)
from PyQt5.QtGui import QIcon, QPixmap, QFont
from PyQt5.QtCore import Qt, QSize

from src.ui.login_dialog import LoginDialog
from src.ui.cafe_tab import CafeTab
from src.ui.playstation_tab import PlayStationTab
from src.ui.reports_tab import ReportsTab
from src.ui.settings_tab import SettingsTab
# from src.ui.inventory_tab import InventoryTab

class MainWindow(QMainWindow):
    """Main window class"""

    def __init__(self, db_manager, config):
        """Initialize the main window"""
        super().__init__()

        self.db_manager = db_manager
        self.config = config
        self.current_user = None

        # Initialize UI
        self.init_ui()

        # Show login dialog
        self.show_login_dialog()

    def init_ui(self):
        """Initialize the user interface"""
        # Set window properties
        self.setWindowTitle(self.config.get("app_name", "محمد الاشرافى"))
        self.setMinimumSize(1024, 768)
        self.setWindowState(Qt.WindowMaximized)

        # Create central widget and main layout
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)

        # Create header with logo and title
        self.create_header()

        # Create tab widget
        self.tab_widget = QTabWidget()
        self.main_layout.addWidget(self.tab_widget)

        # Create tabs
        self.create_tabs()

        # Create status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")

        # Create toolbar and menu
        self.create_toolbar_and_menu()

    def create_header(self):
        """Create the header with logo and title"""
        header_layout = QHBoxLayout()

        # Logo (placeholder for now)
        logo_label = QLabel()
        try:
            logo_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 
                                 "resources", "logo.png")
            if os.path.exists(logo_path):
                logo = QPixmap(logo_path).scaled(80, 80, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                logo_label.setPixmap(logo)
        except Exception:
            logo_label.setText("LOGO")

        logo_label.setMaximumSize(80, 80)
        header_layout.addWidget(logo_label)

        # Title
        title_label = QLabel(self.config.get("app_name", "محمد الاشرافى"))
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(24)
        title_font.setBold(True)
        title_label.setFont(title_font)
        header_layout.addWidget(title_label)

        # User info
        self.user_label = QLabel("User: Not logged in")
        self.user_label.setAlignment(Qt.AlignRight)
        header_layout.addWidget(self.user_label)

        self.main_layout.addLayout(header_layout)

    def create_tabs(self):
        """Create the main tabs"""
        # Cafe tab
        self.cafe_tab = CafeTab(self.db_manager, self.config)
        self.tab_widget.addTab(self.cafe_tab, "المقهى")

        # PlayStation tab
        self.playstation_tab = PlayStationTab(self.db_manager, self.config)
        self.tab_widget.addTab(self.playstation_tab, "بلايستيشن")

        # Inventory tab
        # self.inventory_tab = InventoryTab(self.db_manager, self.config)
        # self.tab_widget.addTab(self.inventory_tab, "المخزون")

        # Reports tab
        self.reports_tab = ReportsTab(self.db_manager, self.config)
        self.tab_widget.addTab(self.reports_tab, "التقارير")

        # Settings tab
        self.settings_tab = SettingsTab(self.db_manager, self.config)
        self.tab_widget.addTab(self.settings_tab, "الإعدادات")

    def create_toolbar_and_menu(self):
        """Create toolbar and menu"""
        # Create toolbar
        self.toolbar = QToolBar("Main Toolbar")
        self.toolbar.setIconSize(QSize(32, 32))
        self.addToolBar(self.toolbar)

        # Add actions
        self.logout_action = QAction("تسجيل الخروج", self)
        self.logout_action.triggered.connect(self.logout)
        self.toolbar.addAction(self.logout_action)

        self.new_order_action = QAction("طلب جديد", self)
        self.new_order_action.triggered.connect(self.new_order)
        self.toolbar.addAction(self.new_order_action)

        self.new_session_action = QAction("جلسة جديدة", self)
        self.new_session_action.triggered.connect(self.new_session)
        self.toolbar.addAction(self.new_session_action)

    def show_login_dialog(self):
        """Show the login dialog"""
        login_dialog = LoginDialog(self.db_manager)
        if login_dialog.exec_() == QDialog.Accepted:
            self.current_user = login_dialog.get_user()
            self.user_label.setText(f"المستخدم: {self.current_user['full_name']}")
            self.status_bar.showMessage(f"مرحبًا بك {self.current_user['full_name']}")
        else:
            # If login is canceled or failed, exit the application
            sys.exit()

    def logout(self):
        """Logout the current user and show login dialog"""
        reply = QMessageBox.question(self, "تسجيل الخروج", "هل أنت متأكد من تسجيل الخروج؟",
                                   QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.current_user = None
            self.user_label.setText("المستخدم: لم يتم تسجيل الدخول")
            self.show_login_dialog()

    def new_order(self):
        """Switch to cafe tab and start new order"""
        self.tab_widget.setCurrentIndex(0)  # Switch to cafe tab
        self.cafe_tab.new_order()

    def new_session(self):
        """Switch to PlayStation tab and start new session"""
        self.tab_widget.setCurrentIndex(1)  # Switch to PlayStation tab
        self.playstation_tab.new_session()

    def closeEvent(self, event):
        """Handle window close event"""
        reply = QMessageBox.question(self, "الخروج", "هل أنت متأكد من الخروج من التطبيق؟",
                                   QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()
