#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نسخة مبسطة من نظام نقاط البيع
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import os
from datetime import datetime

class SimplePOS:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🏪 نظام نقاط البيع - محمد الاشرافى")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2c3e50')
        self.root.state('zoomed')
        
        # التأكد من ظهور النافذة
        self.root.lift()
        self.root.focus_force()
        
        self.setup_database()
        self.create_interface()
        
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        if not os.path.exists('data'):
            os.makedirs('data')
            
        self.conn = sqlite3.connect('data/pos_database.db')
        self.cursor = self.conn.cursor()
        
        # إنشاء جدول المنتجات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                price REAL NOT NULL,
                category TEXT DEFAULT 'cafe'
            )
        ''')
        
        # إضافة منتجات تجريبية
        products = [
            ('قهوة عربية', 15.0, 'cafe'),
            ('شاي أحمر', 10.0, 'cafe'),
            ('عصير برتقال', 12.0, 'cafe'),
            ('ساندويش', 25.0, 'cafe'),
            ('بلايستيشن ساعة', 20.0, 'gaming')
        ]
        
        for product in products:
            self.cursor.execute("INSERT OR IGNORE INTO products (name, price, category) VALUES (?, ?, ?)", product)
        
        self.conn.commit()
        
    def create_interface(self):
        """إنشاء الواجهة"""
        # العنوان
        header = tk.Frame(self.root, bg='#34495e', height=80)
        header.pack(fill='x')
        header.pack_propagate(False)
        
        title = tk.Label(header, text="🏪 نظام نقاط البيع - محمد الاشرافى",
                        font=('Arial', 18, 'bold'), bg='#34495e', fg='white')
        title.pack(pady=20)
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#ecf0f1')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # التبويبات
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill='both', expand=True)
        
        # تبويب المقهى
        cafe_frame = tk.Frame(notebook, bg='white')
        notebook.add(cafe_frame, text='☕ المقهى')
        self.create_cafe_tab(cafe_frame)
        
        # تبويب البلايستيشن
        ps_frame = tk.Frame(notebook, bg='white')
        notebook.add(ps_frame, text='🎮 البلايستيشن')
        self.create_ps_tab(ps_frame)
        
        # تبويب المخزون
        inventory_frame = tk.Frame(notebook, bg='white')
        notebook.add(inventory_frame, text='📦 المخزون')
        self.create_inventory_tab(inventory_frame)
        
        # تبويب التقارير
        reports_frame = tk.Frame(notebook, bg='white')
        notebook.add(reports_frame, text='📊 التقارير')
        self.create_reports_tab(reports_frame)
        
    def create_cafe_tab(self, parent):
        """إنشاء تبويب المقهى"""
        # قائمة المنتجات
        products_frame = tk.LabelFrame(parent, text="المنتجات", font=('Arial', 12, 'bold'))
        products_frame.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        
        # عرض المنتجات
        self.cursor.execute("SELECT * FROM products WHERE category='cafe'")
        products = self.cursor.fetchall()
        
        for i, (id, name, price, category) in enumerate(products):
            btn = tk.Button(products_frame, text=f"{name}\n{price} ريال",
                           font=('Arial', 10), bg='#3498db', fg='white',
                           width=15, height=3,
                           command=lambda p=name, pr=price: self.add_to_order(p, pr))
            btn.grid(row=i//3, column=i%3, padx=5, pady=5)
        
        # الطلب الحالي
        order_frame = tk.LabelFrame(parent, text="الطلب الحالي", font=('Arial', 12, 'bold'))
        order_frame.pack(side='right', fill='y', padx=10, pady=10)
        
        self.order_listbox = tk.Listbox(order_frame, width=30, height=15)
        self.order_listbox.pack(padx=10, pady=10)
        
        # المجموع
        self.total_label = tk.Label(order_frame, text="المجموع: 0 ريال",
                                   font=('Arial', 14, 'bold'))
        self.total_label.pack(pady=10)
        
        # أزرار
        btn_frame = tk.Frame(order_frame)
        btn_frame.pack(pady=10)
        
        tk.Button(btn_frame, text="💳 دفع", bg='#27ae60', fg='white',
                 font=('Arial', 12, 'bold'), command=self.process_payment).pack(pady=5)
        tk.Button(btn_frame, text="🗑️ مسح", bg='#e74c3c', fg='white',
                 font=('Arial', 12, 'bold'), command=self.clear_order).pack(pady=5)
        
        self.order_items = []
        self.total = 0
        
    def create_ps_tab(self, parent):
        """إنشاء تبويب البلايستيشن"""
        tk.Label(parent, text="🎮 إدارة محطات البلايستيشن",
                font=('Arial', 16, 'bold')).pack(pady=20)
        
        # محطات البلايستيشن
        stations_frame = tk.Frame(parent)
        stations_frame.pack(expand=True)
        
        for i in range(1, 7):
            station_frame = tk.LabelFrame(stations_frame, text=f"محطة {i}")
            station_frame.grid(row=(i-1)//3, column=(i-1)%3, padx=10, pady=10)
            
            tk.Label(station_frame, text="متوقفة", font=('Arial', 12)).pack(pady=10)
            tk.Button(station_frame, text="بدء", bg='#27ae60', fg='white').pack(pady=5)
            tk.Button(station_frame, text="إيقاف", bg='#e74c3c', fg='white').pack(pady=5)
        
    def create_inventory_tab(self, parent):
        """إنشاء تبويب المخزون"""
        tk.Label(parent, text="📦 إدارة المخزون",
                font=('Arial', 16, 'bold')).pack(pady=20)
        
        # عرض المنتجات
        tree = ttk.Treeview(parent, columns=('name', 'price', 'category'), show='headings')
        tree.heading('name', text='اسم المنتج')
        tree.heading('price', text='السعر')
        tree.heading('category', text='الفئة')
        tree.pack(fill='both', expand=True, padx=20, pady=20)
        
        # تحميل البيانات
        self.cursor.execute("SELECT name, price, category FROM products")
        for row in self.cursor.fetchall():
            tree.insert('', 'end', values=row)
        
    def create_reports_tab(self, parent):
        """إنشاء تبويب التقارير"""
        tk.Label(parent, text="📊 التقارير والإحصائيات",
                font=('Arial', 16, 'bold')).pack(pady=20)
        
        tk.Label(parent, text="قريباً...", font=('Arial', 14)).pack(pady=50)
        
    def add_to_order(self, name, price):
        """إضافة منتج للطلب"""
        self.order_items.append((name, price))
        self.order_listbox.insert('end', f"{name} - {price} ريال")
        self.total += price
        self.total_label.config(text=f"المجموع: {self.total} ريال")
        
    def clear_order(self):
        """مسح الطلب"""
        self.order_listbox.delete(0, 'end')
        self.order_items = []
        self.total = 0
        self.total_label.config(text="المجموع: 0 ريال")
        
    def process_payment(self):
        """معالجة الدفع"""
        if not self.order_items:
            messagebox.showwarning("تحذير", "لا توجد عناصر في الطلب")
            return
            
        messagebox.showinfo("نجح", f"تم الدفع بنجاح\nالمجموع: {self.total} ريال")
        self.clear_order()
        
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

def main():
    app = SimplePOS()
    app.run()

if __name__ == "__main__":
    main()
