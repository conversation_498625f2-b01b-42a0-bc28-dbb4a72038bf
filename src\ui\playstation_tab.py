#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
وحدة تبويب البلايستيشن لنظام محمد الاشرافى
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
                            QTableWidget, QTableWidgetItem, QComboBox, QSpinBox,
                            QTimeEdit, QFormLayout, QGroupBox, QMessageBox, QHeaderView,
                            QSplitter, QDialog, QDialogButtonBox, QLineEdit)
from PyQt5.QtCore import Qt, QTime, QDateTime, QTimer

class PlayStationSession:
    """Class representing a PlayStation gaming session"""

    def __init__(self, station_id, station_name, start_time=None, status="active"):
        self.id = None  # Database ID, will be set when saved
        self.station_id = station_id
        self.station_name = station_name
        self.start_time = start_time or QDateTime.currentDateTime()
        self.end_time = None
        self.duration_minutes = 0
        self.rate_per_hour = 0
        self.total_cost = 0
        self.status = status
        self.customer_name = ""
        self.notes = ""

        # Create a timer to update the elapsed time
        self.timer = QTimer()
        self.timer.setInterval(60000)  # Update every minute
        self.timer.timeout.connect(self.update_duration)
        self.timer.start()

    def update_duration(self):
        """Update the duration of the session"""
        if self.status == "active":
            current_time = QDateTime.currentDateTime()
            self.duration_minutes = self.start_time.secsTo(current_time) // 60
            self.update_cost()

    def update_cost(self):
        """Update the cost of the session"""
        self.total_cost = (self.duration_minutes / 60) * self.rate_per_hour

    def end_session(self):
        """End the gaming session"""
        self.end_time = QDateTime.currentDateTime()
        self.duration_minutes = self.start_time.secsTo(self.end_time) // 60
        self.update_cost()
        self.status = "completed"
        self.timer.stop()

    def to_dict(self):
        """Convert session to dictionary for database storage"""
        return {
            "id": self.id,
            "station_id": self.station_id,
            "start_time": self.start_time.toString(Qt.ISODate),
            "end_time": self.end_time.toString(Qt.ISODate) if self.end_time else None,
            "duration_minutes": self.duration_minutes,
            "rate_per_hour": self.rate_per_hour,
            "total_cost": self.total_cost,
            "status": self.status,
            "customer_name": self.customer_name,
            "notes": self.notes
        }

class PlayStationTab(QWidget):
    """PlayStation management tab"""

    def __init__(self, db_manager, config):
        """Initialize the PlayStation tab"""
        super().__init__()

        self.db_manager = db_manager
        self.config = config
        self.stations = []
        self.active_sessions = {}  # Dictionary of active sessions: {station_id: session}

        # Initialize UI
        self.init_ui()

        # Load PlayStation stations
        self.load_stations()

        # Load active sessions
        self.load_active_sessions()

        # Create timer to update sessions every minute
        self.update_timer = QTimer(self)
        self.update_timer.setInterval(60000)  # Update every minute
        self.update_timer.timeout.connect(self.update_sessions_display)
        self.update_timer.start()

    def init_ui(self):
        """Initialize the user interface"""
        main_layout = QVBoxLayout(self)

        # Header
        header_layout = QHBoxLayout()
        title_label = QLabel("إدارة البلايستيشن")
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold;")
        header_layout.addWidget(title_label)

        # Add buttons for management
        self.add_station_btn = QPushButton("إضافة جهاز")
        self.add_station_btn.clicked.connect(self.add_station)
        header_layout.addWidget(self.add_station_btn)

        self.view_history_btn = QPushButton("سجل الجلسات")
        self.view_history_btn.clicked.connect(self.view_session_history)
        header_layout.addWidget(self.view_history_btn)

        # Add to main layout
        main_layout.addLayout(header_layout)

        # Create splitter for stations and session details
        splitter = QSplitter(Qt.Horizontal)

        # Stations area (left side)
        self.stations_widget = QWidget()
        stations_layout = QVBoxLayout(self.stations_widget)
        stations_layout.setContentsMargins(0, 0, 0, 0)

        # Stations grid will be created dynamically
        self.stations_layout = QHBoxLayout()
        stations_layout.addLayout(self.stations_layout)
        stations_layout.addStretch()

        # Session details area (right side)
        self.details_widget = QWidget()
        details_layout = QVBoxLayout(self.details_widget)

        # Session details form
        self.details_group = QGroupBox("تفاصيل الجلسة")
        details_form = QFormLayout(self.details_group)

        self.station_label = QLabel("لم يتم اختيار محطة")
        details_form.addRow("المحطة:", self.station_label)

        self.status_label = QLabel("غير نشط")
        details_form.addRow("الحالة:", self.status_label)

        self.start_time_label = QLabel("-")
        details_form.addRow("وقت البدء:", self.start_time_label)

        self.duration_label = QLabel("0 دقيقة")
        details_form.addRow("المدة:", self.duration_label)

        self.rate_spin = QSpinBox()
        self.rate_spin.setRange(0, 100)
        self.rate_spin.setValue(int(self.config.get("default_ps_rate", 20)))
        self.rate_spin.setSuffix(" " + self.config.get("currency_symbol", "ريال") + "/ساعة")
        self.rate_spin.valueChanged.connect(self.update_session_rate)
        details_form.addRow("السعر:", self.rate_spin)

        self.cost_label = QLabel("0 " + self.config.get("currency_symbol", "ريال"))
        details_form.addRow("التكلفة:", self.cost_label)

        self.customer_name = QLineEdit()
        self.customer_name.textChanged.connect(self.update_customer_name)
        details_form.addRow("اسم العميل:", self.customer_name)

        self.notes_edit = QLineEdit()
        self.notes_edit.textChanged.connect(self.update_session_notes)
        details_form.addRow("ملاحظات:", self.notes_edit)

        details_layout.addWidget(self.details_group)

        # Control buttons
        buttons_layout = QHBoxLayout()

        self.start_btn = QPushButton("بدء جلسة")
        self.start_btn.clicked.connect(self.start_session)
        buttons_layout.addWidget(self.start_btn)

        self.end_btn = QPushButton("إنهاء جلسة")
        self.end_btn.clicked.connect(self.end_session)
        self.end_btn.setEnabled(False)
        buttons_layout.addWidget(self.end_btn)

        details_layout.addLayout(buttons_layout)
        details_layout.addStretch()

        # Add widgets to splitter
        splitter.addWidget(self.stations_widget)
        splitter.addWidget(self.details_widget)
        splitter.setStretchFactor(0, 2)
        splitter.setStretchFactor(1, 1)

        main_layout.addWidget(splitter)

    def load_stations(self):
        """Load PlayStation stations from database"""
        try:
            # In a real implementation, this would load from database
            # For now, create some sample stations
            self.stations = [
                {"id": 1, "name": "PS5-1", "type": "PS5", "status": "available"},
                {"id": 2, "name": "PS5-2", "type": "PS5", "status": "available"},
                {"id": 3, "name": "PS4-1", "type": "PS4", "status": "available"},
                {"id": 4, "name": "PS4-2", "type": "PS4", "status": "available"},
            ]
            self.update_stations_display()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل أجهزة البلايستيشن: {str(e)}")

    def update_stations_display(self):
        """Update the stations display"""
        # Clear existing buttons
        for i in reversed(range(self.stations_layout.count())):
            widget = self.stations_layout.itemAt(i).widget()
            if widget is not None:
                widget.deleteLater()

        # Add station buttons
        for station in self.stations:
            station_btn = QPushButton(f"{station['name']}")
            station_btn.setMinimumSize(150, 100)

            if station['id'] in self.active_sessions:
                station_btn.setStyleSheet("background-color: #ff9966;")  # Orange for active
                session = self.active_sessions[station['id']]
                station_btn.setText(f"{station['name']}\nنشط: {session.duration_minutes} دقيقة")
            else:
                station_btn.setStyleSheet("background-color: #99cc99;")  # Green for available

            station_btn.clicked.connect(lambda checked, s=station: self.select_station(s))
            self.stations_layout.addWidget(station_btn)

    def load_active_sessions(self):
        """Load active sessions from database"""
        try:
            # In a real implementation, this would load from database
            # For now, we'll just have an empty dict
            self.active_sessions = {}
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل جلسات البلايستيشن النشطة: {str(e)}")

    def select_station(self, station):
        """Select a station and show its details"""
        self.current_station = station
        self.station_label.setText(station['name'])

        if station['id'] in self.active_sessions:
            # Show active session details
            session = self.active_sessions[station['id']]
            self.status_label.setText("نشط")
            self.start_time_label.setText(session.start_time.toString("hh:mm:ss"))
            self.duration_label.setText(f"{session.duration_minutes} دقيقة")
            self.rate_spin.setValue(session.rate_per_hour)
            self.cost_label.setText(f"{session.total_cost:.2f} {self.config.get('currency_symbol', 'ريال')}")
            self.customer_name.setText(session.customer_name)
            self.notes_edit.setText(session.notes)

            # Update buttons
            self.start_btn.setEnabled(False)
            self.end_btn.setEnabled(True)
        else:
            # Show empty details
            self.status_label.setText("غير نشط")
            self.start_time_label.setText("-")
            self.duration_label.setText("0 دقيقة")
            self.rate_spin.setValue(int(self.config.get("default_ps_rate", 20)))
            self.cost_label.setText(f"0 {self.config.get('currency_symbol', 'ريال')}")
            self.customer_name.setText("")
            self.notes_edit.setText("")

            # Update buttons
            self.start_btn.setEnabled(True)
            self.end_btn.setEnabled(False)

    def start_session(self):
        """Start a new PlayStation session"""
        if hasattr(self, 'current_station'):
            try:
                # Create new session
                session = PlayStationSession(
                    self.current_station['id'],
                    self.current_station['name']
                )
                session.rate_per_hour = self.rate_spin.value()
                session.customer_name = self.customer_name.text()
                session.notes = self.notes_edit.text()

                # Update session in the dictionary
                self.active_sessions[self.current_station['id']] = session

                # Save to database in real implementation
                # self.db_manager.save_ps_session(session.to_dict())

                # Update UI
                self.update_stations_display()
                self.select_station(self.current_station)

                QMessageBox.information(self, "تم", f"تم بدء جلسة جديدة على {self.current_station['name']}")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء بدء الجلسة: {str(e)}")

    def end_session(self):
        """End the active PlayStation session"""
        if hasattr(self, 'current_station') and self.current_station['id'] in self.active_sessions:
            try:
                # End session
                session = self.active_sessions[self.current_station['id']]
                session.end_session()

                # Create receipt or final bill
                message = f"تم إنهاء الجلسة بنجاح\n\n"
                message += f"المحطة: {session.station_name}\n"
                message += f"المدة: {session.duration_minutes} دقيقة\n"
                message += f"السعر: {session.rate_per_hour} {self.config.get('currency_symbol', 'ريال')}/ساعة\n"
                message += f"المجموع: {session.total_cost:.2f} {self.config.get('currency_symbol', 'ريال')}\n"

                # Remove from active sessions
                del self.active_sessions[self.current_station['id']]

                # Save to database in real implementation
                # self.db_manager.update_ps_session(session.to_dict())

                # Update UI
                self.update_stations_display()
                self.select_station(self.current_station)

                # Show receipt
                QMessageBox.information(self, "تم إنهاء الجلسة", message)
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنهاء الجلسة: {str(e)}")

    def update_session_rate(self):
        """Update the rate of the current session"""
        if hasattr(self, 'current_station') and self.current_station['id'] in self.active_sessions:
            session = self.active_sessions[self.current_station['id']]
            session.rate_per_hour = self.rate_spin.value()
            session.update_cost()
            self.cost_label.setText(f"{session.total_cost:.2f} {self.config.get('currency_symbol', 'ريال')}")

    def update_customer_name(self):
        """Update the customer name of the current session"""
        if hasattr(self, 'current_station') and self.current_station['id'] in self.active_sessions:
            session = self.active_sessions[self.current_station['id']]
            session.customer_name = self.customer_name.text()

    def update_session_notes(self):
        """Update the notes of the current session"""
        if hasattr(self, 'current_station') and self.current_station['id'] in self.active_sessions:
            session = self.active_sessions[self.current_station['id']]
            session.notes = self.notes_edit.text()

    def update_sessions_display(self):
        """Update the display for all active sessions"""
        for session_id, session in self.active_sessions.items():
            session.update_duration()

        if hasattr(self, 'current_station') and self.current_station['id'] in self.active_sessions:
            session = self.active_sessions[self.current_station['id']]
            self.duration_label.setText(f"{session.duration_minutes} دقيقة")
            self.cost_label.setText(f"{session.total_cost:.2f} {self.config.get('currency_symbol', 'ريال')}")

        self.update_stations_display()

    def add_station(self):
        """Add a new PlayStation station"""
        # In a real implementation, this would open a dialog to add a new station
        QMessageBox.information(self, "إضافة جهاز", "هذه الوظيفة غير متوفرة في النسخة التجريبية")

    def view_session_history(self):
        """View the history of PlayStation sessions"""
        # In a real implementation, this would open a dialog showing session history
        QMessageBox.information(self, "سجل الجلسات", "هذه الوظيفة غير متوفرة في النسخة التجريبية")

    def new_session(self):
        """Start a new session (called from main window)"""
        # Select the first available station if any
        for station in self.stations:
            if station['id'] not in self.active_sessions:
                self.select_station(station)
                self.start_session()
                break
