#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف تشغيل مباشر للنظام بدون تسجيل دخول (للاختبار)
"""

import sys
import os

def main():
    """تشغيل النظام مباشرة بدون تسجيل دخول"""
    try:
        print("🚀 تشغيل مباشر لنظام نقاط البيع...")
        
        # إضافة المجلد الحالي إلى مسار Python
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        # استيراد البرنامج الرئيسي
        from main_tkinter import POSApp
        
        # بيانات مستخدم افتراضية
        user_info = {
            'username': 'admin',
            'full_name': 'المدير العام',
            'role': 'admin'
        }
        
        print("🏪 تشغيل البرنامج الرئيسي...")
        
        # إنشاء وتشغيل البرنامج الرئيسي
        app = POSApp(user_info=user_info)
        app.run()
        
        print("👋 تم إغلاق النظام")
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الملفات: {e}")
        input("اضغط Enter للخروج...")
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
