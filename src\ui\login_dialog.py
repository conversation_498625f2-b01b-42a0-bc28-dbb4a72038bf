#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Login dialog for محمد الاشرافى POS System
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QPushButton, QMessageBox)
from PyQt5.QtGui import QIcon, QPixmap, QFont
from PyQt5.QtCore import Qt

class LoginDialog(QDialog):
    """Login dialog class"""

    def __init__(self, db_manager):
        """Initialize login dialog"""
        super().__init__()

        self.db_manager = db_manager
        self.user = None

        # Initialize UI
        self.init_ui()

    def init_ui(self):
        """Initialize user interface"""
        # Set window properties
        self.setWindowTitle("تسجيل الدخول")
        self.setFixedSize(400, 300)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)

        # Create main layout
        layout = QVBoxLayout()
        self.setLayout(layout)

        # Header/logo
        header_label = QLabel("محمد الاشرافى")
        header_label.setAlignment(Qt.AlignCenter)
        header_font = QFont()
        header_font.setPointSize(18)
        header_font.setBold(True)
        header_label.setFont(header_font)
        layout.addWidget(header_label)

        subtitle_label = QLabel("نظام نقاط البيع")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_font = QFont()
        subtitle_font.setPointSize(12)
        subtitle_label.setFont(subtitle_font)
        layout.addWidget(subtitle_label)

        # Add some space
        layout.addSpacing(20)

        # Username field
        username_layout = QHBoxLayout()
        username_label = QLabel("اسم المستخدم:")
        username_label.setMinimumWidth(100)
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("أدخل اسم المستخدم")
        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_edit)
        layout.addLayout(username_layout)

        # Password field
        password_layout = QHBoxLayout()
        password_label = QLabel("كلمة المرور:")
        password_label.setMinimumWidth(100)
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("أدخل كلمة المرور")
        self.password_edit.setEchoMode(QLineEdit.Password)
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_edit)
        layout.addLayout(password_layout)

        # Add some space
        layout.addSpacing(20)

        # Login button
        button_layout = QHBoxLayout()
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.clicked.connect(self.login)
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.login_button)
        button_layout.addWidget(self.cancel_button)
        layout.addLayout(button_layout)

        # Set default button and focus
        self.login_button.setDefault(True)
        self.username_edit.setFocus()

    def login(self):
        """Handle login button click"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()

        # Validate inputs
        if not username or not password:
            QMessageBox.warning(self, "خطأ", "الرجاء إدخال اسم المستخدم وكلمة المرور")
            return

        # Authenticate user
        user = self.db_manager.authenticate_user(username, password)

        if user:
            self.user = user
            self.accept()
        else:
            QMessageBox.warning(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_edit.clear()
            self.password_edit.setFocus()

    def get_user(self):
        """Get the authenticated user"""
        return self.user
