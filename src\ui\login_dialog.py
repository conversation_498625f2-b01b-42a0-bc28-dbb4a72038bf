#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Login dialog for محمد الاشرافى POS System
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                             QLineEdit, QPushButton, QMessageBox, QFrame,
                             QGraphicsDropShadowEffect, QSpacerItem, QSizePolicy)
from PyQt5.QtGui import QIcon, QPixmap, QFont, QPalette, QBrush, QColor, QPainter
from PyQt5.QtCore import Qt, QPropertyAnimation, QEasingCurve, pyqtProperty

class LoginDialog(QDialog):
    """Login dialog class"""

    def __init__(self, db_manager):
        """Initialize login dialog"""
        super().__init__()

        self.db_manager = db_manager
        self.user = None

        # Initialize UI
        self.init_ui()

    def init_ui(self):
        """Initialize user interface"""
        # Set window properties
        self.setWindowTitle("تسجيل الدخول - محمد الاشرافى")
        self.setFixedSize(450, 550)
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # Create main layout
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        self.setLayout(main_layout)

        # Create main frame with shadow effect
        self.main_frame = QFrame()
        self.main_frame.setObjectName("loginFrame")
        self.main_frame.setStyleSheet("""
            QFrame#loginFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                            stop: 0 rgba(255, 255, 255, 0.95),
                                            stop: 1 rgba(240, 248, 255, 0.95));
                border-radius: 20px;
                border: 2px solid rgba(52, 152, 219, 0.3);
            }
        """)

        # Add shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 10)
        self.main_frame.setGraphicsEffect(shadow)

        main_layout.addWidget(self.main_frame)

        # Layout for the frame content
        layout = QVBoxLayout(self.main_frame)
        layout.setSpacing(20)
        layout.setContentsMargins(40, 40, 40, 40)

        # Logo/Header section
        logo_frame = QFrame()
        logo_layout = QVBoxLayout(logo_frame)
        logo_layout.setSpacing(10)

        # Main title
        header_label = QLabel("محمد الاشرافى")
        header_label.setAlignment(Qt.AlignCenter)
        header_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-size: 32px;
                font-weight: bold;
                margin: 10px 0;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            }
        """)
        logo_layout.addWidget(header_label)

        # Subtitle
        subtitle_label = QLabel("نظام نقاط البيع والبلايستيشن")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #34495e;
                font-size: 16px;
                font-weight: normal;
                margin-bottom: 20px;
            }
        """)
        logo_layout.addWidget(subtitle_label)

        # Welcome message
        welcome_label = QLabel("مرحباً بك، يرجى تسجيل الدخول للمتابعة")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 14px;
                margin-bottom: 10px;
            }
        """)
        logo_layout.addWidget(welcome_label)

        layout.addWidget(logo_frame)

        # Input fields section
        input_frame = QFrame()
        input_layout = QVBoxLayout(input_frame)
        input_layout.setSpacing(20)

        # Username field
        username_container = QFrame()
        username_container.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 12px;
                padding: 5px;
            }
            QFrame:focus-within {
                border: 2px solid #3498db;
            }
        """)
        username_layout = QVBoxLayout(username_container)
        username_layout.setContentsMargins(15, 10, 15, 10)

        username_label = QLabel("اسم المستخدم")
        username_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 12px;
                font-weight: bold;
                margin-bottom: 5px;
            }
        """)
        username_layout.addWidget(username_label)

        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("أدخل اسم المستخدم")
        self.username_edit.setStyleSheet("""
            QLineEdit {
                border: none;
                font-size: 14px;
                color: #2c3e50;
                background: transparent;
                padding: 5px 0;
            }
        """)
        username_layout.addWidget(self.username_edit)
        input_layout.addWidget(username_container)

        # Password field
        password_container = QFrame()
        password_container.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #bdc3c7;
                border-radius: 12px;
                padding: 5px;
            }
            QFrame:focus-within {
                border: 2px solid #3498db;
            }
        """)
        password_layout = QVBoxLayout(password_container)
        password_layout.setContentsMargins(15, 10, 15, 10)

        password_label = QLabel("كلمة المرور")
        password_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 12px;
                font-weight: bold;
                margin-bottom: 5px;
            }
        """)
        password_layout.addWidget(password_label)

        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("أدخل كلمة المرور")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setStyleSheet("""
            QLineEdit {
                border: none;
                font-size: 14px;
                color: #2c3e50;
                background: transparent;
                padding: 5px 0;
            }
        """)
        password_layout.addWidget(self.password_edit)
        input_layout.addWidget(password_container)

        layout.addWidget(input_frame)

        # Buttons section
        button_frame = QFrame()
        button_layout = QVBoxLayout(button_frame)
        button_layout.setSpacing(15)

        # Login button
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #3498db, stop: 1 #2980b9);
                color: white;
                border: none;
                border-radius: 12px;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
                min-height: 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #5dade2, stop: 1 #3498db);
            }
            QPushButton:pressed {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #2980b9, stop: 1 #21618c);
            }
        """)
        self.login_button.clicked.connect(self.login)
        button_layout.addWidget(self.login_button)

        # Cancel button
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background: transparent;
                color: #7f8c8d;
                border: 2px solid #bdc3c7;
                border-radius: 12px;
                padding: 12px;
                font-size: 14px;
                font-weight: bold;
                min-height: 20px;
            }
            QPushButton:hover {
                background: #ecf0f1;
                color: #2c3e50;
                border: 2px solid #95a5a6;
            }
        """)
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)

        layout.addWidget(button_frame)

        # Add spacer at bottom
        layout.addItem(QSpacerItem(20, 20, QSizePolicy.Minimum, QSizePolicy.Expanding))

        # Set default button and focus
        self.login_button.setDefault(True)
        self.username_edit.setFocus()

        # Connect Enter key to login
        self.username_edit.returnPressed.connect(self.password_edit.setFocus)
        self.password_edit.returnPressed.connect(self.login)

    def login(self):
        """Handle login button click"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()

        # Validate inputs
        if not username or not password:
            QMessageBox.warning(self, "خطأ", "الرجاء إدخال اسم المستخدم وكلمة المرور")
            return

        # Authenticate user
        user = self.db_manager.authenticate_user(username, password)

        if user:
            self.user = user
            self.accept()
        else:
            QMessageBox.warning(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_edit.clear()
            self.password_edit.setFocus()

    def get_user(self):
        """Get the authenticated user"""
        return self.user
