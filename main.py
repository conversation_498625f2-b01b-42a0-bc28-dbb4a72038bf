
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف تشغيل نظام نقاط البيع - محمد الاشرافى
نظام متكامل لإدارة المقهى والبلايستيشن مع تسجيل الدخول
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from login_system import LoginWindow
    from main_tkinter import POSApp

    def main():
        """الدالة الرئيسية لتشغيل النظام"""
        print("🏪 بدء تشغيل نظام نقاط البيع - محمد الاشرافى...")
        print("📋 تحميل نظام تسجيل الدخول...")

        def on_login_success(user_info):
            """تشغيل البرنامج الرئيسي بعد تسجيل الدخول بنجاح"""
            print(f"✅ تم تسجيل الدخول بنجاح: {user_info['full_name']}")
            print("🚀 تشغيل البرنامج الرئيسي...")

            # إنشاء وتشغيل البرنامج الرئيسي
            app = POSApp(user_info=user_info)
            app.run()

        # إنشاء وتشغيل نافذة تسجيل الدخول
        login_window = LoginWindow(on_success_callback=on_login_success)
        login_window.run()

        print("👋 تم إغلاق النظام")

    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"❌ خطأ في استيراد الملفات: {e}")
    print("تأكد من وجود الملفات التالية:")
    print("- login_system.py")
    print("- main_tkinter.py")
    print("\n💡 نصيحة: استخدم 'python run_pos.py' لتشغيل النظام")
    input("اضغط Enter للخروج...")

except Exception as e:
    print(f"❌ خطأ غير متوقع: {e}")
    input("اضغط Enter للخروج...")
