
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
محمد الاشرافى - Café & PlayStation POS System
Main application entry point
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTranslator, QLocale
from src.ui.main_window import MainWindow
from src.utils.config import initialize_config
from src.database.db_manager import DatabaseManager

def main():
    """Main application entry point"""
    # Initialize the application
    app = QApplication(sys.argv)

    # Set up Arabic translation
    translator = QTranslator()
    translator.load("qt_ar", QLocale.system().name())
    app.installTranslator(translator)

    # Initialize configuration
    config = initialize_config()

    # Initialize database
    db_manager = DatabaseManager()
    db_manager.initialize_database()

    # Create and show the main window
    window = MainWindow(db_manager, config)
    window.show()

    # Start the application event loop
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
