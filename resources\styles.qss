/* محمد الاشرافى POS System - Modern Arabic Theme */

/* Main Application Styling */
QMainWindow {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                stop: 0 #1e3c72, stop: 1 #2a5298);
    color: #ffffff;
    font-family: "Segoe UI", "Tahoma", "Arial";
    font-size: 12px;
}

/* Tab Widget Styling */
QTabWidget::pane {
    border: 2px solid #34495e;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.95);
    margin-top: 5px;
}

QTabWidget::tab-bar {
    alignment: center;
}

QTabBar::tab {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #3498db, stop: 1 #2980b9);
    color: white;
    padding: 12px 20px;
    margin: 2px;
    border-radius: 8px;
    font-weight: bold;
    font-size: 14px;
    min-width: 120px;
}

QTabBar::tab:selected {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #e74c3c, stop: 1 #c0392b);
    color: white;
}

QTabBar::tab:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #f39c12, stop: 1 #e67e22);
}

/* Button Styling */
QPushButton {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #27ae60, stop: 1 #229954);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: bold;
    font-size: 13px;
    min-height: 35px;
}

QPushButton:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #2ecc71, stop: 1 #27ae60);
    transform: translateY(-2px);
}

QPushButton:pressed {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #229954, stop: 1 #1e8449);
}

QPushButton:disabled {
    background: #95a5a6;
    color: #7f8c8d;
}

/* Special Button Types */
QPushButton[class="danger"] {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #e74c3c, stop: 1 #c0392b);
}

QPushButton[class="warning"] {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #f39c12, stop: 1 #e67e22);
}

QPushButton[class="info"] {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #3498db, stop: 1 #2980b9);
}

/* Input Fields */
QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox {
    background: white;
    border: 2px solid #bdc3c7;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 13px;
    color: #2c3e50;
}

QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {
    border: 2px solid #3498db;
    background: #ecf0f1;
}

/* ComboBox */
QComboBox {
    background: white;
    border: 2px solid #bdc3c7;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 13px;
    color: #2c3e50;
    min-width: 150px;
}

QComboBox:focus {
    border: 2px solid #3498db;
}

QComboBox::drop-down {
    border: none;
    width: 30px;
}

QComboBox::down-arrow {
    image: url(resources/icons/arrow-down.png);
    width: 12px;
    height: 12px;
}

/* Table Widget */
QTableWidget {
    background: white;
    border: 1px solid #bdc3c7;
    border-radius: 8px;
    gridline-color: #ecf0f1;
    font-size: 12px;
    color: #2c3e50;
}

QTableWidget::item {
    padding: 8px;
    border-bottom: 1px solid #ecf0f1;
}

QTableWidget::item:selected {
    background: #3498db;
    color: white;
}

QTableWidget::item:hover {
    background: #ecf0f1;
}

QHeaderView::section {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #34495e, stop: 1 #2c3e50);
    color: white;
    padding: 10px;
    border: none;
    font-weight: bold;
    font-size: 13px;
}

/* Labels */
QLabel {
    color: #2c3e50;
    font-size: 13px;
}

QLabel[class="title"] {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    margin: 10px 0;
}

QLabel[class="subtitle"] {
    font-size: 16px;
    font-weight: bold;
    color: #34495e;
    margin: 5px 0;
}

/* Group Box */
QGroupBox {
    font-weight: bold;
    font-size: 14px;
    color: #2c3e50;
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    margin: 10px 0;
    padding-top: 15px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 10px 0 10px;
    background: white;
}

/* Status Bar */
QStatusBar {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                stop: 0 #34495e, stop: 1 #2c3e50);
    color: white;
    border-top: 1px solid #bdc3c7;
    font-size: 12px;
    padding: 5px;
}

/* Scroll Bar */
QScrollBar:vertical {
    background: #ecf0f1;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background: #bdc3c7;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background: #95a5a6;
}

/* Dialog Styling */
QDialog {
    background: white;
    border-radius: 10px;
}

/* Menu Bar */
QMenuBar {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                stop: 0 #2c3e50, stop: 1 #34495e);
    color: white;
    border-bottom: 1px solid #bdc3c7;
}

QMenuBar::item {
    padding: 8px 15px;
    background: transparent;
}

QMenuBar::item:selected {
    background: #3498db;
    border-radius: 4px;
}

/* Tool Bar */
QToolBar {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                stop: 0 #ecf0f1, stop: 1 #bdc3c7);
    border: 1px solid #95a5a6;
    spacing: 5px;
    padding: 5px;
}

/* Progress Bar */
QProgressBar {
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    text-align: center;
    font-weight: bold;
    background: white;
}

QProgressBar::chunk {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                stop: 0 #27ae60, stop: 1 #2ecc71);
    border-radius: 6px;
}

/* Splitter */
QSplitter::handle {
    background: #bdc3c7;
}

QSplitter::handle:horizontal {
    width: 3px;
}

QSplitter::handle:vertical {
    height: 3px;
}

/* Animation Effects */
QPushButton, QTabBar::tab {
    transition: all 0.3s ease;
}
