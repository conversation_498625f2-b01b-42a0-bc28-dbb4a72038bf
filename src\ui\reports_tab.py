#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
وحدة تبويب التقارير لنظام محمد الاشرافى
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
                            QTableWidget, QTableWidgetItem, QComboBox, QDateEdit,
                            QFormLayout, QGroupBox, QMessageBox, QHeaderView,
                            QSplitter, QTabWidget)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

import matplotlib
matplotlib.use('Qt5Agg')
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.pyplot as plt
import pandas as pd
import random

class MatplotlibCanvas(FigureCanvas):
    """Matplotlib Canvas for rendering charts"""
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        self.axes = self.fig.add_subplot(111)
        super(MatplotlibCanvas, self).__init__(self.fig)

class ReportsTab(QWidget):
    """Reports and analytics tab"""

    def __init__(self, db_manager, config):
        """Initialize the reports tab"""
        super().__init__()

        self.db_manager = db_manager
        self.config = config

        # Initialize UI
        self.init_ui()

    def init_ui(self):
        """Initialize the user interface"""
        main_layout = QVBoxLayout(self)

        # Header
        header_layout = QHBoxLayout()
        title_label = QLabel("التقارير والتحليلات")
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold;")
        header_layout.addWidget(title_label)

        # Date range selector
        date_group = QGroupBox("نطاق التاريخ")
        date_layout = QFormLayout()

        self.from_date = QDateEdit()
        self.from_date.setDate(QDate.currentDate().addDays(-30))
        self.from_date.setCalendarPopup(True)
        date_layout.addRow("من:", self.from_date)

        self.to_date = QDateEdit()
        self.to_date.setDate(QDate.currentDate())
        self.to_date.setCalendarPopup(True)
        date_layout.addRow("إلى:", self.to_date)

        self.update_btn = QPushButton("تحديث التقارير")
        self.update_btn.clicked.connect(self.update_reports)
        date_layout.addRow("", self.update_btn)

        date_group.setLayout(date_layout)
        header_layout.addWidget(date_group)

        main_layout.addLayout(header_layout)

        # Create tab widget for different reports
        self.reports_tabs = QTabWidget()

        # Sales tab
        self.sales_tab = QWidget()
        sales_layout = QVBoxLayout(self.sales_tab)

        # Sales summary
        self.sales_summary = QGroupBox("ملخص المبيعات")
        summary_layout = QHBoxLayout()

        self.total_sales_label = QLabel("إجمالي المبيعات: 0 " + self.config.get("currency_symbol", "ريال"))
        self.total_sales_label.setStyleSheet("font-size: 14pt;")
        summary_layout.addWidget(self.total_sales_label)

        self.cafe_sales_label = QLabel("مبيعات المقهى: 0 " + self.config.get("currency_symbol", "ريال"))
        self.cafe_sales_label.setStyleSheet("font-size: 14pt;")
        summary_layout.addWidget(self.cafe_sales_label)

        self.ps_sales_label = QLabel("مبيعات البلايستيشن: 0 " + self.config.get("currency_symbol", "ريال"))
        self.ps_sales_label.setStyleSheet("font-size: 14pt;")
        summary_layout.addWidget(self.ps_sales_label)

        self.sales_summary.setLayout(summary_layout)
        sales_layout.addWidget(self.sales_summary)

        # Sales chart
        self.sales_chart_container = QWidget()
        chart_layout = QVBoxLayout(self.sales_chart_container)

        self.sales_canvas = MatplotlibCanvas(width=5, height=4, dpi=100)
        chart_layout.addWidget(self.sales_canvas)

        sales_layout.addWidget(self.sales_chart_container)

        # Product sales table
        self.product_table = QTableWidget()
        self.product_table.setColumnCount(4)
        self.product_table.setHorizontalHeaderLabels(["المنتج", "الكمية", "السعر", "الإجمالي"])
        self.product_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        sales_layout.addWidget(self.product_table)

        self.reports_tabs.addTab(self.sales_tab, "تقرير المبيعات")

        # PlayStation tab
        self.ps_tab = QWidget()
        ps_layout = QVBoxLayout(self.ps_tab)

        # PlayStation summary
        self.ps_summary = QGroupBox("ملخص البلايستيشن")
        ps_summary_layout = QHBoxLayout()

        self.total_sessions_label = QLabel("إجمالي الجلسات: 0")
        self.total_sessions_label.setStyleSheet("font-size: 14pt;")
        ps_summary_layout.addWidget(self.total_sessions_label)

        self.avg_session_label = QLabel("متوسط مدة الجلسة: 0 دقيقة")
        self.avg_session_label.setStyleSheet("font-size: 14pt;")
        ps_summary_layout.addWidget(self.avg_session_label)

        self.ps_summary.setLayout(ps_summary_layout)
        ps_layout.addWidget(self.ps_summary)

        # PlayStation usage chart
        self.ps_chart_container = QWidget()
        ps_chart_layout = QVBoxLayout(self.ps_chart_container)

        self.ps_canvas = MatplotlibCanvas(width=5, height=4, dpi=100)
        ps_chart_layout.addWidget(self.ps_canvas)

        ps_layout.addWidget(self.ps_chart_container)

        # PlayStation sessions table
        self.sessions_table = QTableWidget()
        self.sessions_table.setColumnCount(5)
        self.sessions_table.setHorizontalHeaderLabels(["الجهاز", "بداية الجلسة", "المدة", "السعر", "الإجمالي"])
        self.sessions_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        ps_layout.addWidget(self.sessions_table)

        self.reports_tabs.addTab(self.ps_tab, "تقرير البلايستيشن")

        main_layout.addWidget(self.reports_tabs)

        # Load initial data
        self.update_reports()

    def update_reports(self):
        """Update all reports with current date range"""
        try:
            # In a real implementation, this would query the database
            # For now, let's populate with mock data
            self.generate_mock_data()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحديث التقارير: {str(e)}")

    def generate_mock_data(self):
        """Generate mock data for demonstration"""
        # Sales summary
        cafe_sales = random.randint(5000, 20000)
        ps_sales = random.randint(3000, 15000)
        total_sales = cafe_sales + ps_sales

        self.total_sales_label.setText(f"إجمالي المبيعات: {total_sales} {self.config.get('currency_symbol', 'ريال')}")
        self.cafe_sales_label.setText(f"مبيعات المقهى: {cafe_sales} {self.config.get('currency_symbol', 'ريال')}")
        self.ps_sales_label.setText(f"مبيعات البلايستيشن: {ps_sales} {self.config.get('currency_symbol', 'ريال')}")

        # Sales chart
        days = 10
        dates = [QDate.currentDate().addDays(-i).toString("MM-dd") for i in range(days)]
        dates.reverse()

        cafe_data = [random.randint(500, 2000) for _ in range(days)]
        ps_data = [random.randint(300, 1500) for _ in range(days)]

        self.sales_canvas.axes.clear()
        self.sales_canvas.axes.bar(dates, cafe_data, label="مقهى", alpha=0.7, color="blue")
        self.sales_canvas.axes.bar(dates, ps_data, bottom=cafe_data, label="بلايستيشن", alpha=0.7, color="green")
        self.sales_canvas.axes.set_title("المبيعات اليومية")
        self.sales_canvas.axes.set_ylabel(f"المبيعات ({self.config.get('currency_symbol', 'ريال')})")
        self.sales_canvas.axes.set_xlabel("التاريخ")
        self.sales_canvas.axes.legend()
        self.sales_canvas.draw()

        # Product sales table
        products = [
            {"name": "قهوة عربية", "quantity": random.randint(10, 50), "price": 15},
            {"name": "قهوة أمريكية", "quantity": random.randint(20, 60), "price": 18},
            {"name": "كابتشينو", "quantity": random.randint(15, 45), "price": 20},
            {"name": "شاي", "quantity": random.randint(30, 70), "price": 10},
            {"name": "عصير برتقال", "quantity": random.randint(20, 50), "price": 15},
            {"name": "سندويش جبن", "quantity": random.randint(10, 40), "price": 22},
            {"name": "كيك شوكولاتة", "quantity": random.randint(5, 30), "price": 25},
        ]

        self.product_table.setRowCount(len(products))
        for i, product in enumerate(products):
            self.product_table.setItem(i, 0, QTableWidgetItem(product["name"]))
            self.product_table.setItem(i, 1, QTableWidgetItem(str(product["quantity"])))
            self.product_table.setItem(i, 2, QTableWidgetItem(f"{product['price']} {self.config.get('currency_symbol', 'ريال')}"))
            total = product["quantity"] * product["price"]
            self.product_table.setItem(i, 3, QTableWidgetItem(f"{total} {self.config.get('currency_symbol', 'ريال')}"))

        # PlayStation data
        total_sessions = random.randint(50, 150)
        avg_session = random.randint(60, 180)

        self.total_sessions_label.setText(f"إجمالي الجلسات: {total_sessions}")
        self.avg_session_label.setText(f"متوسط مدة الجلسة: {avg_session} دقيقة")

        # PlayStation chart - station usage
        stations = ["PS5-1", "PS5-2", "PS4-1", "PS4-2"]
        usage_hours = [random.randint(20, 100) for _ in range(len(stations))]

        self.ps_canvas.axes.clear()
        self.ps_canvas.axes.pie(usage_hours, labels=stations, autopct='%1.1f%%', startangle=90)
        self.ps_canvas.axes.set_title("استخدام محطات البلايستيشن")
        self.ps_canvas.draw()

        # PlayStation sessions table
        sessions = [
            {"station": "PS5-1", "start": "2023-07-01 14:30", "duration": 90, "rate": 25},
            {"station": "PS4-2", "start": "2023-07-01 16:45", "duration": 120, "rate": 20},
            {"station": "PS5-2", "start": "2023-07-01 18:20", "duration": 60, "rate": 25},
            {"station": "PS4-1", "start": "2023-07-01 19:15", "duration": 150, "rate": 20},
            {"station": "PS5-1", "start": "2023-07-01 21:30", "duration": 180, "rate": 25},
        ]

        self.sessions_table.setRowCount(len(sessions))
        for i, session in enumerate(sessions):
            self.sessions_table.setItem(i, 0, QTableWidgetItem(session["station"]))
            self.sessions_table.setItem(i, 1, QTableWidgetItem(session["start"]))
            self.sessions_table.setItem(i, 2, QTableWidgetItem(f"{session['duration']} دقيقة"))
            self.sessions_table.setItem(i, 3, QTableWidgetItem(f"{session['rate']} {self.config.get('currency_symbol', 'ريال')}/ساعة"))
            total = session["duration"] / 60 * session["rate"]
            self.sessions_table.setItem(i, 4, QTableWidgetItem(f"{total:.2f} {self.config.get('currency_symbol', 'ريال')}"))
